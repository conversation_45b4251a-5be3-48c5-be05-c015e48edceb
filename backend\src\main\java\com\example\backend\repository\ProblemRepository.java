package com.example.backend.repository;

import com.example.backend.model.Problem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 题目数据访问接口
 * 提供对题目数据的CRUD操作
 */
@Repository
public interface ProblemRepository extends JpaRepository<Problem, Long> {
    
    /**
     * 根据难度查找题目
     * @param difficulty 难度级别
     * @return 题目列表
     */
    List<Problem> findByDifficulty(Problem.Difficulty difficulty);
    
    /**
     * 根据标题关键字搜索题目
     * @param keyword 关键字
     * @return 题目列表
     */
    @Query("SELECT p FROM Problem p WHERE p.title LIKE %:keyword% OR p.description LIKE %:keyword%")
    List<Problem> findByTitleOrDescriptionContaining(@Param("keyword") String keyword);
    
    /**
     * 查找所有题目并按难度排序
     * @return 题目列表
     */
    List<Problem> findAllByOrderByDifficultyAsc();
}
