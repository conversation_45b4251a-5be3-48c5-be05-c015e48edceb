package com.example.backend.service;

import com.example.backend.model.Problem;
import com.example.backend.model.Submission;
import com.example.backend.repository.SubmissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 代码评测服务
 * 提供代码编译、执行和评测功能
 */
@Service
public class JudgeService {
    
    @Autowired
    private SubmissionRepository submissionRepository;
    
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir") + File.separator + "algo_arena";
    
    /**
     * 提交代码进行评测
     * @param problem 题目信息
     * @param language 编程语言
     * @param code 代码内容
     * @return 提交记录
     */
    public Submission submitCode(Problem problem, String language, String code) {
        Submission submission = new Submission(problem, language, code);
        submission = submissionRepository.save(submission);
        
        // 异步评测
        judgeSubmissionAsync(submission);
        
        return submission;
    }
    
    /**
     * 异步评测提交
     * @param submission 提交记录
     */
    private void judgeSubmissionAsync(Submission submission) {
        new Thread(() -> {
            try {
                judgeSubmission(submission);
            } catch (Exception e) {
                submission.setStatus(Submission.Status.RUNTIME_ERROR);
                submission.setErrorMessage("评测系统错误: " + e.getMessage());
                submission.setJudgeTime(LocalDateTime.now());
                submissionRepository.save(submission);
            }
        }).start();
    }
    
    /**
     * 评测提交的代码
     * @param submission 提交记录
     */
    private void judgeSubmission(Submission submission) {
        submission.setStatus(Submission.Status.JUDGING);
        submission.setJudgeTime(LocalDateTime.now());
        submissionRepository.save(submission);
        
        try {
            // 创建临时目录
            createTempDirectory();
            
            // 编译代码
            CompileResult compileResult = compileCode(submission);
            if (!compileResult.success) {
                submission.setStatus(Submission.Status.COMPILE_ERROR);
                submission.setCompileOutput(compileResult.output);
                submissionRepository.save(submission);
                return;
            }
            
            // 运行测试用例
            runTestCases(submission);
            
        } catch (Exception e) {
            submission.setStatus(Submission.Status.RUNTIME_ERROR);
            submission.setErrorMessage(e.getMessage());
        } finally {
            submissionRepository.save(submission);
            // 清理临时文件
            cleanupTempFiles();
        }
    }
    
    /**
     * 编译代码
     * @param submission 提交记录
     * @return 编译结果
     */
    private CompileResult compileCode(Submission submission) {
        try {
            String fileName = getFileName(submission.getLanguage());
            Path sourceFile = Paths.get(TEMP_DIR, fileName);
            Files.write(sourceFile, submission.getCode().getBytes());
            
            String[] compileCommand = getCompileCommand(submission.getLanguage(), fileName);
            if (compileCommand == null) {
                // 解释型语言，无需编译
                return new CompileResult(true, "");
            }
            
            ProcessBuilder pb = new ProcessBuilder(compileCommand);
            pb.directory(new File(TEMP_DIR));
            Process process = pb.start();
            
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                return new CompileResult(false, "编译超时");
            }
            
            String output = readProcessOutput(process);
            return new CompileResult(process.exitValue() == 0, output);
            
        } catch (Exception e) {
            return new CompileResult(false, "编译错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行测试用例
     * @param submission 提交记录
     */
    private void runTestCases(Submission submission) {
        String testCases = submission.getProblem().getTestCases();
        if (testCases == null || testCases.trim().isEmpty()) {
            // 使用示例输入输出作为测试用例
            testCases = submission.getProblem().getSampleInput() + "\n---\n" + 
                       submission.getProblem().getSampleOutput();
        }
        
        String[] cases = testCases.split("---");
        int passedCases = 0;
        int totalCases = cases.length / 2;
        
        long maxExecutionTime = 0;
        long maxMemoryUsage = 0;
        
        for (int i = 0; i < cases.length; i += 2) {
            if (i + 1 >= cases.length) break;
            
            String input = cases[i].trim();
            String expectedOutput = cases[i + 1].trim();
            
            ExecutionResult result = executeCode(submission, input);
            
            if (result.success) {
                maxExecutionTime = Math.max(maxExecutionTime, result.executionTime);
                maxMemoryUsage = Math.max(maxMemoryUsage, result.memoryUsage);
                
                if (result.output.trim().equals(expectedOutput)) {
                    passedCases++;
                } else {
                    submission.setStatus(Submission.Status.WRONG_ANSWER);
                    submission.setErrorMessage("输出不匹配。期望: " + expectedOutput + 
                                             ", 实际: " + result.output.trim());
                    break;
                }
            } else {
                if (result.timeout) {
                    submission.setStatus(Submission.Status.TIME_LIMIT_EXCEEDED);
                } else if (result.memoryExceeded) {
                    submission.setStatus(Submission.Status.MEMORY_LIMIT_EXCEEDED);
                } else {
                    submission.setStatus(Submission.Status.RUNTIME_ERROR);
                }
                submission.setErrorMessage(result.errorMessage);
                break;
            }
        }
        
        submission.setScore(passedCases);
        submission.setTotalTestCases(totalCases);
        submission.setExecutionTime(maxExecutionTime);
        submission.setMemoryUsage(maxMemoryUsage);
        
        if (submission.getStatus() == Submission.Status.JUDGING) {
            submission.setStatus(passedCases == totalCases ? 
                               Submission.Status.ACCEPTED : Submission.Status.WRONG_ANSWER);
        }
    }
    
    /**
     * 执行代码
     * @param submission 提交记录
     * @param input 输入数据
     * @return 执行结果
     */
    private ExecutionResult executeCode(Submission submission, String input) {
        try {
            String[] runCommand = getRunCommand(submission.getLanguage());
            ProcessBuilder pb = new ProcessBuilder(runCommand);
            pb.directory(new File(TEMP_DIR));
            
            long startTime = System.currentTimeMillis();
            Process process = pb.start();
            
            // 写入输入
            try (OutputStreamWriter writer = new OutputStreamWriter(process.getOutputStream())) {
                writer.write(input);
                writer.flush();
            }
            
            // 等待执行完成（设置超时）
            boolean finished = process.waitFor(submission.getProblem().getTimeLimit(), TimeUnit.MILLISECONDS);
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (!finished) {
                process.destroyForcibly();
                return new ExecutionResult(false, "", executionTime, 0, true, false, "执行超时");
            }
            
            String output = readProcessOutput(process);
            String errorOutput = readProcessError(process);
            
            if (process.exitValue() != 0) {
                return new ExecutionResult(false, output, executionTime, 0, false, false, 
                                         "运行错误: " + errorOutput);
            }
            
            // 简单的内存使用估算（实际项目中需要更精确的测量）
            long memoryUsage = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            
            return new ExecutionResult(true, output, executionTime, memoryUsage, false, false, "");
            
        } catch (Exception e) {
            return new ExecutionResult(false, "", 0, 0, false, false, "执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有提交记录
     * @return 提交记录列表
     */
    public List<Submission> getAllSubmissions() {
        return submissionRepository.findAll();
    }
    
    /**
     * 根据ID获取提交记录
     * @param id 提交ID
     * @return 提交记录
     */
    public Optional<Submission> getSubmissionById(Long id) {
        return submissionRepository.findById(id);
    }
    
    /**
     * 根据题目ID获取提交记录
     * @param problemId 题目ID
     * @return 提交记录列表
     */
    public List<Submission> getSubmissionsByProblemId(Long problemId) {
        return submissionRepository.findByProblemIdOrderBySubmitTimeDesc(problemId);
    }
    
    // 辅助方法
    private void createTempDirectory() throws IOException {
        Path tempPath = Paths.get(TEMP_DIR);
        if (!Files.exists(tempPath)) {
            Files.createDirectories(tempPath);
        }
    }
    
    private void cleanupTempFiles() {
        try {
            Path tempPath = Paths.get(TEMP_DIR);
            if (Files.exists(tempPath)) {
                Files.walk(tempPath)
                     .filter(Files::isRegularFile)
                     .forEach(file -> {
                         try {
                             Files.deleteIfExists(file);
                         } catch (IOException e) {
                             // 忽略删除错误
                         }
                     });
            }
        } catch (IOException e) {
            // 忽略清理错误
        }
    }
    
    private String getFileName(String language) {
        switch (language.toLowerCase()) {
            case "java": return "Solution.java";
            case "python": return "solution.py";
            case "cpp": case "c++": return "solution.cpp";
            case "c": return "solution.c";
            case "javascript": return "solution.js";
            default: return "solution.txt";
        }
    }
    
    private String[] getCompileCommand(String language, String fileName) {
        switch (language.toLowerCase()) {
            case "java":
                return new String[]{"javac", fileName};
            case "cpp":
            case "c++":
                return new String[]{"g++", "-o", "solution", fileName};
            case "c":
                return new String[]{"gcc", "-o", "solution", fileName};
            default:
                return null; // 解释型语言
        }
    }
    
    private String[] getRunCommand(String language) {
        switch (language.toLowerCase()) {
            case "java":
                return new String[]{"java", "Solution"};
            case "python":
                return new String[]{"python", "solution.py"};
            case "cpp":
            case "c++":
            case "c":
                return new String[]{"./solution"};
            case "javascript":
                return new String[]{"node", "solution.js"};
            default:
                throw new RuntimeException("不支持的编程语言: " + language);
        }
    }
    
    private String readProcessOutput(Process process) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()))) {
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            return output.toString();
        }
    }
    
    private String readProcessError(Process process) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getErrorStream()))) {
            StringBuilder error = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                error.append(line).append("\n");
            }
            return error.toString();
        }
    }
    
    // 内部类
    private static class CompileResult {
        final boolean success;
        final String output;
        
        CompileResult(boolean success, String output) {
            this.success = success;
            this.output = output;
        }
    }
    
    private static class ExecutionResult {
        final boolean success;
        final String output;
        final long executionTime;
        final long memoryUsage;
        final boolean timeout;
        final boolean memoryExceeded;
        final String errorMessage;
        
        ExecutionResult(boolean success, String output, long executionTime, 
                       long memoryUsage, boolean timeout, boolean memoryExceeded, 
                       String errorMessage) {
            this.success = success;
            this.output = output;
            this.executionTime = executionTime;
            this.memoryUsage = memoryUsage;
            this.timeout = timeout;
            this.memoryExceeded = memoryExceeded;
            this.errorMessage = errorMessage;
        }
    }
}
