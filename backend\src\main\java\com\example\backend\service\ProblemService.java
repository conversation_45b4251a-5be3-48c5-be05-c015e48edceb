package com.example.backend.service;

import com.example.backend.model.Problem;
import com.example.backend.repository.ProblemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 题目管理服务
 * 提供题目的业务逻辑处理
 */
@Service
public class ProblemService {
    
    @Autowired
    private ProblemRepository problemRepository;
    
    /**
     * 获取所有题目
     * @return 题目列表
     */
    public List<Problem> getAllProblems() {
        return problemRepository.findAllByOrderByDifficultyAsc();
    }
    
    /**
     * 根据ID获取题目
     * @param id 题目ID
     * @return 题目信息
     */
    public Optional<Problem> getProblemById(Long id) {
        return problemRepository.findById(id);
    }
    
    /**
     * 根据难度获取题目
     * @param difficulty 难度级别
     * @return 题目列表
     */
    public List<Problem> getProblemsByDifficulty(Problem.Difficulty difficulty) {
        return problemRepository.findByDifficulty(difficulty);
    }
    
    /**
     * 搜索题目
     * @param keyword 关键字
     * @return 题目列表
     */
    public List<Problem> searchProblems(String keyword) {
        return problemRepository.findByTitleOrDescriptionContaining(keyword);
    }
    
    /**
     * 创建新题目
     * @param problem 题目信息
     * @return 保存的题目
     */
    public Problem createProblem(Problem problem) {
        return problemRepository.save(problem);
    }
    
    /**
     * 更新题目
     * @param id 题目ID
     * @param problemDetails 题目详情
     * @return 更新后的题目
     */
    public Problem updateProblem(Long id, Problem problemDetails) {
        Problem problem = problemRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在: " + id));
        
        problem.setTitle(problemDetails.getTitle());
        problem.setDescription(problemDetails.getDescription());
        problem.setDifficulty(problemDetails.getDifficulty());
        problem.setInputFormat(problemDetails.getInputFormat());
        problem.setOutputFormat(problemDetails.getOutputFormat());
        problem.setSampleInput(problemDetails.getSampleInput());
        problem.setSampleOutput(problemDetails.getSampleOutput());
        problem.setTestCases(problemDetails.getTestCases());
        problem.setTimeLimit(problemDetails.getTimeLimit());
        problem.setMemoryLimit(problemDetails.getMemoryLimit());
        
        return problemRepository.save(problem);
    }
    
    /**
     * 删除题目
     * @param id 题目ID
     */
    public void deleteProblem(Long id) {
        problemRepository.deleteById(id);
    }
}
