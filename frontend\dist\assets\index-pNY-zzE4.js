import{s as ea}from"./d3-CCOk6-7m.js";import{e as Ar,K as ta,a as Ia}from"./monaco-editor-DIU5cU5g.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const n of i)if(n.type==="childList")for(const l of n.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function r(i){const n={};return i.integrity&&(n.integrity=i.integrity),i.referrerPolicy&&(n.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?n.credentials="include":i.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(i){if(i.ep)return;i.ep=!0;const n=r(i);fetch(i.href,n)}})();const Xa=!1;var qr=Array.isArray,Xs=Array.prototype.indexOf,ha=Array.from,oa=Object.defineProperty,Nt=Object.getOwnPropertyDescriptor,Qa=Object.getOwnPropertyDescriptors,Qs=Object.prototype,Zs=Array.prototype,ga=Object.getPrototypeOf,Oa=Object.isExtensible;function en(e){return e()}function ca(e){for(var t=0;t<e.length;t++)e[t]()}function tn(){var e,t,r=new Promise((s,i)=>{e=s,t=i});return{promise:r,resolve:e,reject:t}}const Ye=2,ma=4,$r=8,wr=16,xt=32,Lt=64,Za=128,Qe=256,Nr=512,Ce=1024,Ze=2048,zt=4096,it=8192,jt=16384,ba=32768,Fr=65536,Da=1<<17,rn=1<<18,ya=1<<19,wa=1<<20,va=1<<21,xa=1<<22,It=1<<23,bt=Symbol("$state"),an=Symbol("legacy props"),za=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"};function sn(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function es(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function nn(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function ln(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function on(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function cn(e){throw new Error("https://svelte.dev/e/effect_orphan")}function vn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function un(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function fn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function dn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function pn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const Ur=1,Br=2,ts=4,_n=8,hn=16,gn=1,mn=2,bn=4,yn=8,wn=16,xn=1,zn=2,Se=Symbol(),kn="http://www.w3.org/1999/xhtml";function En(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let Sn=!1;function rs(e){return e===this.v}function Cn(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function as(e){return!Cn(e,this.v)}let nr=!1,Tn=!1;function An(){nr=!0}let ge=null;function Ir(e){ge=e}function et(e,t=!1,r){ge={p:ge,c:null,e:null,s:e,x:null,l:nr&&!t?{s:null,u:null,$:[]}:null}}function tt(e){var t=ge,r=t.e;if(r!==null){t.e=null;for(var s of r)ps(s)}return e!==void 0&&(t.x=e),ge=t.p,e??{}}function ir(){return!nr||ge!==null&&ge.l===null}const Rn=new WeakMap;function Mn(e){var t=re;if(t===null)return se.f|=It,e;if((t.f&ba)===0){if((t.f&Za)===0)throw!t.parent&&e instanceof Error&&ss(e),e;t.b.error(e)}else ka(e,t)}function ka(e,t){for(;t!==null;){if((t.f&Za)!==0)try{t.b.error(e);return}catch(r){e=r}t=t.parent}throw e instanceof Error&&ss(e),e}function ss(e){const t=Rn.get(e);t&&(oa(e,"message",{value:t.message}),oa(e,"stack",{value:t.stack}))}let Or=[];function Nn(){var e=Or;Or=[],ca(e)}function Ea(e){Or.length===0&&queueMicrotask(Nn),Or.push(e)}function In(){for(var e=re.b;e!==null&&!e.has_pending_snippet();)e=e.parent;return e===null&&sn(),e}function Vr(e){var t=Ye|Ze,r=se!==null&&(se.f&Ye)!==0?se:null;return re===null||r!==null&&(r.f&Qe)!==0?t|=Qe:re.f|=ya,{ctx:ge,deps:null,effects:null,equals:rs,f:t,fn:e,reactions:null,rv:0,v:Se,wv:0,parent:r??re,ac:null}}function On(e,t){let r=re;r===null&&nn();var s=r.b,i=void 0,n=mr(Se),l=null,v=!se;return Wn(()=>{try{var o=e()}catch(x){o=Promise.reject(x)}var u=()=>o;i=l?.then(u,u)??Promise.resolve(o),l=i;var d=oe,E=s.pending;v&&(s.update_pending_count(1),E||d.increment());const g=(x,m=void 0)=>{l=null,E||d.activate(),m?m!==za&&(n.f|=It,tr(n,m)):((n.f&It)!==0&&(n.f^=It),tr(n,x)),v&&(s.update_pending_count(-1),E||d.decrement()),ls()};if(i.then(g,x=>g(null,x||"unknown")),d)return()=>{queueMicrotask(()=>d.neuter())}}),new Promise(o=>{function u(d){function E(){d===i?o(n):u(i)}d.then(E,E)}u(i)})}function Qt(e){const t=Vr(e);return t.equals=as,t}function ns(e){var t=e.effects;if(t!==null){e.effects=null;for(var r=0;r<t.length;r+=1)wt(t[r])}}function Dn(e){for(var t=e.parent;t!==null;){if((t.f&Ye)===0)return t;t=t.parent}return null}function Sa(e){var t,r=re;At(Dn(e));try{ns(e),t=ks(e)}finally{At(r)}return t}function is(e){var t=Sa(e);if(e.equals(t)||(e.v=t,e.wv=xs()),!$t)if(Zt!==null)Zt.set(e,e.v);else{var r=(Ct||(e.f&Qe)!==0)&&e.deps!==null?zt:Ce;De(e,r)}}function Pn(e,t,r){const s=ir()?Vr:Qt;if(t.length===0){r(e.map(s));return}var i=oe,n=re,l=Ln(),v=In();Promise.all(t.map(o=>On(o))).then(o=>{i?.activate(),l();try{r([...e.map(s),...o])}catch(u){(n.f&jt)===0&&ka(u,n)}i?.deactivate(),ls()}).catch(o=>{v.error(o)})}function Ln(){var e=re,t=se,r=ge;return function(){At(e),ft(t),Ir(r)}}function ls(){At(null),ft(null),Ir(null)}const dr=new Set;let oe=null,Mr=null,Zt=null,Pa=new Set,Dr=[];function os(){const e=Dr.shift();Dr.length>0&&queueMicrotask(os),e()}let gr=[],Ca=null,ua=!1;class er{current=new Map;#s=new Map;#n=new Set;#e=0;#c=null;#v=!1;#r=[];#i=[];#a=[];#t=[];#l=[];#u=[];#f=[];skipped_effects=new Set;process(t){gr=[],Mr=null;var r=null;if(dr.size>1){r=new Map,Zt=new Map;for(const[n,l]of this.current)r.set(n,{v:n.v,wv:n.wv}),n.v=l;for(const n of dr)if(n!==this)for(const[l,v]of n.#s)r.has(l)||(r.set(l,{v:l.v,wv:l.wv}),l.v=v)}for(const n of t)this.#p(n);if(this.#r.length===0&&this.#e===0){this.#d();var s=this.#a,i=this.#t;this.#a=[],this.#t=[],this.#l=[],Mr=oe,oe=null,La(s),La(i),oe===null?oe=this:dr.delete(this),this.#c?.resolve()}else this.#o(this.#a),this.#o(this.#t),this.#o(this.#l);if(r){for(const[n,{v:l,wv:v}]of r)n.wv<=v&&(n.v=l);Zt=null}for(const n of this.#r)Xt(n);for(const n of this.#i)Xt(n);this.#r=[],this.#i=[]}#p(t){t.f^=Ce;for(var r=t.first;r!==null;){var s=r.f,i=(s&(xt|Lt))!==0,n=i&&(s&Ce)!==0,l=n||(s&it)!==0||this.skipped_effects.has(r);if(!l&&r.fn!==null){if(i)r.f^=Ce;else if((s&Ce)===0)if((s&ma)!==0)this.#t.push(r);else if((s&xa)!==0){var v=r.b?.pending?this.#i:this.#r;v.push(r)}else zr(r)&&((r.f&wr)!==0&&this.#l.push(r),Xt(r));var o=r.first;if(o!==null){r=o;continue}}var u=r.parent;for(r=r.next;r===null&&u!==null;)r=u.next,u=u.parent}}#o(t){for(const r of t)((r.f&Ze)!==0?this.#u:this.#f).push(r),De(r,Ce);t.length=0}capture(t,r){this.#s.has(t)||this.#s.set(t,r),this.current.set(t,t.v)}activate(){oe=this}deactivate(){oe=null,Mr=null;for(const t of Pa)if(Pa.delete(t),t(),oe!==null)break}neuter(){this.#v=!0}flush(){gr.length>0?jn():this.#d(),oe===this&&(this.#e===0&&dr.delete(this),this.deactivate())}#d(){if(!this.#v)for(const t of this.#n)t();this.#n.clear()}increment(){this.#e+=1}decrement(){if(this.#e-=1,this.#e===0){for(const t of this.#u)De(t,Ze),Pt(t);for(const t of this.#f)De(t,zt),Pt(t);this.#a=[],this.#t=[],this.flush()}else this.deactivate()}add_callback(t){this.#n.add(t)}settled(){return(this.#c??=tn()).promise}static ensure(){if(oe===null){const t=oe=new er;dr.add(oe),er.enqueue(()=>{oe===t&&t.flush()})}return oe}static enqueue(t){Dr.length===0&&queueMicrotask(os),Dr.unshift(t)}}function jn(){var e=Kt;ua=!0;try{var t=0;for($a(!0);gr.length>0;){var r=er.ensure();if(t++>1e3){var s,i;qn()}r.process(gr),Ot.clear()}}finally{ua=!1,$a(e),Ca=null}}function qn(){try{vn()}catch(e){ka(e,Ca)}}function La(e){var t=e.length;if(t!==0){for(var r=0;r<t;){var s=e[r++];if((s.f&(jt|it))===0&&zr(s)){var i=oe?oe.current.size:0;if(Xt(s),s.deps===null&&s.first===null&&s.nodes_start===null&&(s.teardown===null&&s.ac===null?ms(s):s.fn=null),oe!==null&&oe.current.size>i&&(s.f&wa)!==0)break}}for(;r<t;)Pt(e[r++])}}function Pt(e){for(var t=Ca=e;t.parent!==null;){t=t.parent;var r=t.f;if(ua&&t===re&&(r&wr)!==0)return;if((r&(Lt|xt))!==0){if((r&Ce)===0)return;t.f^=Ce}}gr.push(t)}const Ot=new Map;function mr(e,t){var r={f:0,v:e,reactions:null,equals:rs,rv:0,wv:0};return r}function St(e,t){const r=mr(e);return Kn(r),r}function X(e,t=!1,r=!0){const s=mr(e);return t||(s.equals=as),nr&&r&&ge!==null&&ge.l!==null&&(ge.l.s??=[]).push(s),s}function h(e,t,r=!1){se!==null&&(!ut||(se.f&Da)!==0)&&ir()&&(se.f&(Ye|wr|xa|Da))!==0&&!yt?.includes(e)&&pn();let s=r?Yt(t):t;return tr(e,s)}function tr(e,t){if(!e.equals(t)){var r=e.v;$t?Ot.set(e,t):Ot.set(e,r),e.v=t;var s=er.ensure();s.capture(e,r),(e.f&Ye)!==0&&((e.f&Ze)!==0&&Sa(e),De(e,(e.f&Qe)===0?Ce:zt)),e.wv=xs(),cs(e,Ze),ir()&&re!==null&&(re.f&Ce)!==0&&(re.f&(xt|Lt))===0&&(Xe===null?Xn([e]):Xe.push(e))}return t}function ra(e,t=1){var r=a(e),s=t===1?r++:r--;return h(e,r),s}function aa(e){h(e,e.v+1)}function cs(e,t){var r=e.reactions;if(r!==null)for(var s=ir(),i=r.length,n=0;n<i;n++){var l=r[n],v=l.f;if(!(!s&&l===re)){var o=(v&Ze)===0;o&&De(l,t),(v&Ye)!==0?cs(l,zt):o&&Pt(l)}}}function Yt(e){if(typeof e!="object"||e===null||bt in e)return e;const t=ga(e);if(t!==Qs&&t!==Zs)return e;var r=new Map,s=qr(e),i=St(0),n=Dt,l=v=>{if(Dt===n)return v();var o=se,u=Dt;ft(null),Ua(n);var d=v();return ft(o),Ua(u),d};return s&&r.set("length",St(e.length)),new Proxy(e,{defineProperty(v,o,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&fn();var d=r.get(o);return d===void 0?d=l(()=>{var E=St(u.value);return r.set(o,E),E}):h(d,u.value,!0),!0},deleteProperty(v,o){var u=r.get(o);if(u===void 0){if(o in v){const d=l(()=>St(Se));r.set(o,d),aa(i)}}else h(u,Se),aa(i);return!0},get(v,o,u){if(o===bt)return e;var d=r.get(o),E=o in v;if(d===void 0&&(!E||Nt(v,o)?.writable)&&(d=l(()=>{var x=Yt(E?v[o]:Se),m=St(x);return m}),r.set(o,d)),d!==void 0){var g=a(d);return g===Se?void 0:g}return Reflect.get(v,o,u)},getOwnPropertyDescriptor(v,o){var u=Reflect.getOwnPropertyDescriptor(v,o);if(u&&"value"in u){var d=r.get(o);d&&(u.value=a(d))}else if(u===void 0){var E=r.get(o),g=E?.v;if(E!==void 0&&g!==Se)return{enumerable:!0,configurable:!0,value:g,writable:!0}}return u},has(v,o){if(o===bt)return!0;var u=r.get(o),d=u!==void 0&&u.v!==Se||Reflect.has(v,o);if(u!==void 0||re!==null&&(!d||Nt(v,o)?.writable)){u===void 0&&(u=l(()=>{var g=d?Yt(v[o]):Se,x=St(g);return x}),r.set(o,u));var E=a(u);if(E===Se)return!1}return d},set(v,o,u,d){var E=r.get(o),g=o in v;if(s&&o==="length")for(var x=u;x<E.v;x+=1){var m=r.get(x+"");m!==void 0?h(m,Se):x in v&&(m=l(()=>St(Se)),r.set(x+"",m))}if(E===void 0)(!g||Nt(v,o)?.writable)&&(E=l(()=>St(void 0)),h(E,Yt(u)),r.set(o,E));else{g=E.v!==Se;var O=l(()=>Yt(u));h(E,O)}var P=Reflect.getOwnPropertyDescriptor(v,o);if(P?.set&&P.set.call(d,u),!g){if(s&&typeof o=="string"){var B=r.get("length"),V=Number(o);Number.isInteger(V)&&V>=B.v&&h(B,V+1)}aa(i)}return!0},ownKeys(v){a(i);var o=Reflect.ownKeys(v).filter(E=>{var g=r.get(E);return g===void 0||g.v!==Se});for(var[u,d]of r)d.v!==Se&&!(u in v)&&o.push(u);return o},setPrototypeOf(){dn()}})}function ja(e){try{if(e!==null&&typeof e=="object"&&bt in e)return e[bt]}catch{}return e}function $n(e,t){return Object.is(ja(e),ja(t))}var qa,vs,us,fs;function Fn(){if(qa===void 0){qa=window,vs=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,r=Text.prototype;us=Nt(t,"firstChild").get,fs=Nt(t,"nextSibling").get,Oa(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),Oa(r)&&(r.__t=void 0)}}function qt(e=""){return document.createTextNode(e)}function Pr(e){return us.call(e)}function Gr(e){return fs.call(e)}function c(e,t){return Pr(e)}function ve(e,t){{var r=Pr(e);return r instanceof Comment&&r.data===""?Gr(r):r}}function f(e,t=1,r=!1){let s=e;for(;t--;)s=Gr(s);return s}function Un(e){e.textContent=""}function Ta(){return!1}function ds(e){re===null&&se===null&&cn(),se!==null&&(se.f&Qe)!==0&&re===null&&on(),$t&&ln()}function Bn(e,t){var r=t.last;r===null?t.last=t.first=e:(r.next=e,e.prev=r,t.last=e)}function dt(e,t,r,s=!0){var i=re;i!==null&&(i.f&it)!==0&&(e|=it);var n={ctx:ge,deps:null,nodes_start:null,nodes_end:null,f:e|Ze,first:null,fn:t,last:null,next:null,parent:i,b:i&&i.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(r)try{Xt(n),n.f|=ba}catch(o){throw wt(n),o}else t!==null&&Pt(n);var l=r&&n.deps===null&&n.first===null&&n.nodes_start===null&&n.teardown===null&&(n.f&ya)===0;if(!l&&s&&(i!==null&&Bn(n,i),se!==null&&(se.f&Ye)!==0&&(e&Lt)===0)){var v=se;(v.effects??=[]).push(n)}return n}function Aa(e){const t=dt($r,null,!1);return De(t,Ce),t.teardown=e,t}function fa(e){ds();var t=re.f,r=!se&&(t&xt)!==0&&(t&ba)===0;if(r){var s=ge;(s.e??=[]).push(e)}else return ps(e)}function ps(e){return dt(ma|wa,e,!1)}function Vn(e){return ds(),dt($r|wa,e,!0)}function Gn(e){er.ensure();const t=dt(Lt,e,!0);return(r={})=>new Promise(s=>{r.outro?Hr(t,()=>{wt(t),s(void 0)}):(wt(t),s(void 0))})}function _s(e){return dt(ma,e,!1)}function Tt(e,t){var r=ge,s={effect:null,ran:!1,deps:e};r.l.$.push(s),s.effect=Wr(()=>{e(),!s.ran&&(s.ran=!0,S(t))})}function xr(){var e=ge;Wr(()=>{for(var t of e.l.$){t.deps();var r=t.effect;(r.f&Ce)!==0&&De(r,zt),zr(r)&&Xt(r),t.ran=!1}})}function Wn(e){return dt(xa|ya,e,!0)}function Wr(e,t=0){return dt($r|t,e,!0)}function Y(e,t=[],r=[]){Pn(t,r,s=>{dt($r,()=>e(...s.map(a)),!0)})}function Ra(e,t=0){var r=dt(wr|t,e,!0);return r}function rr(e,t=!0){return dt(xt,e,!0,t)}function hs(e){var t=e.teardown;if(t!==null){const r=$t,s=se;Fa(!0),ft(null);try{t.call(null)}finally{Fa(r),ft(s)}}}function gs(e,t=!1){var r=e.first;for(e.first=e.last=null;r!==null;){r.ac?.abort(za);var s=r.next;(r.f&Lt)!==0?r.parent=null:wt(r,t),r=s}}function Hn(e){for(var t=e.first;t!==null;){var r=t.next;(t.f&xt)===0&&wt(t),t=r}}function wt(e,t=!0){var r=!1;(t||(e.f&rn)!==0)&&e.nodes_start!==null&&e.nodes_end!==null&&(Jn(e.nodes_start,e.nodes_end),r=!0),gs(e,t&&!r),Lr(e,0),De(e,jt);var s=e.transitions;if(s!==null)for(const n of s)n.stop();hs(e);var i=e.parent;i!==null&&i.first!==null&&ms(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function Jn(e,t){for(;e!==null;){var r=e===t?null:Gr(e);e.remove(),e=r}}function ms(e){var t=e.parent,r=e.prev,s=e.next;r!==null&&(r.next=s),s!==null&&(s.prev=r),t!==null&&(t.first===e&&(t.first=s),t.last===e&&(t.last=r))}function Hr(e,t){var r=[];Ma(e,r,!0),bs(r,()=>{wt(e),t&&t()})}function bs(e,t){var r=e.length;if(r>0){var s=()=>--r||t();for(var i of e)i.out(s)}else t()}function Ma(e,t,r){if((e.f&it)===0){if(e.f^=it,e.transitions!==null)for(const l of e.transitions)(l.is_global||r)&&t.push(l);for(var s=e.first;s!==null;){var i=s.next,n=(s.f&Fr)!==0||(s.f&xt)!==0;Ma(s,t,n?r:!1),s=i}}}function Na(e){ys(e,!0)}function ys(e,t){if((e.f&it)!==0){e.f^=it,(e.f&Ce)===0&&(De(e,Ze),Pt(e));for(var r=e.first;r!==null;){var s=r.next,i=(r.f&Fr)!==0||(r.f&xt)!==0;ys(r,i?t:!1),r=s}if(e.transitions!==null)for(const n of e.transitions)(n.is_global||t)&&n.in()}}let Jt=null;function Yn(e){var t=Jt;try{if(Jt=new Set,S(e),t!==null)for(var r of Jt)t.add(r);return Jt}finally{Jt=t}}function ar(e){for(var t of Yn(e))tr(t,t.v)}let Kt=!1;function $a(e){Kt=e}let $t=!1;function Fa(e){$t=e}let se=null,ut=!1;function ft(e){se=e}let re=null;function At(e){re=e}let yt=null;function Kn(e){se!==null&&(yt===null?yt=[e]:yt.push(e))}let Ie=null,We=0,Xe=null;function Xn(e){Xe=e}let ws=1,br=0,Dt=br;function Ua(e){Dt=e}let Ct=!1;function xs(){return++ws}function zr(e){var t=e.f;if((t&Ze)!==0)return!0;if((t&zt)!==0){var r=e.deps,s=(t&Qe)!==0;if(r!==null){var i,n,l=(t&Nr)!==0,v=s&&re!==null&&!Ct,o=r.length;if((l||v)&&(re===null||(re.f&jt)===0)){var u=e,d=u.parent;for(i=0;i<o;i++)n=r[i],(l||!n?.reactions?.includes(u))&&(n.reactions??=[]).push(u);l&&(u.f^=Nr),v&&d!==null&&(d.f&Qe)===0&&(u.f^=Qe)}for(i=0;i<o;i++)if(n=r[i],zr(n)&&is(n),n.wv>e.wv)return!0}(!s||re!==null&&!Ct)&&De(e,Ce)}return!1}function zs(e,t,r=!0){var s=e.reactions;if(s!==null&&!yt?.includes(e))for(var i=0;i<s.length;i++){var n=s[i];(n.f&Ye)!==0?zs(n,t,!1):t===n&&(r?De(n,Ze):(n.f&Ce)!==0&&De(n,zt),Pt(n))}}function ks(e){var t=Ie,r=We,s=Xe,i=se,n=Ct,l=yt,v=ge,o=ut,u=Dt,d=e.f;Ie=null,We=0,Xe=null,Ct=(d&Qe)!==0&&(ut||!Kt||se===null),se=(d&(xt|Lt))===0?e:null,yt=null,Ir(e.ctx),ut=!1,Dt=++br,e.ac!==null&&(e.ac.abort(za),e.ac=null);try{e.f|=va;var E=(0,e.fn)(),g=e.deps;if(Ie!==null){var x;if(Lr(e,We),g!==null&&We>0)for(g.length=We+Ie.length,x=0;x<Ie.length;x++)g[We+x]=Ie[x];else e.deps=g=Ie;if(!Ct||(d&Ye)!==0&&e.reactions!==null)for(x=We;x<g.length;x++)(g[x].reactions??=[]).push(e)}else g!==null&&We<g.length&&(Lr(e,We),g.length=We);if(ir()&&Xe!==null&&!ut&&g!==null&&(e.f&(Ye|zt|Ze))===0)for(x=0;x<Xe.length;x++)zs(Xe[x],e);return i!==null&&i!==e&&(br++,Xe!==null&&(s===null?s=Xe:s.push(...Xe))),(e.f&It)!==0&&(e.f^=It),E}catch(m){return Mn(m)}finally{e.f^=va,Ie=t,We=r,Xe=s,se=i,Ct=n,yt=l,Ir(v),ut=o,Dt=u}}function Qn(e,t){let r=t.reactions;if(r!==null){var s=Xs.call(r,e);if(s!==-1){var i=r.length-1;i===0?r=t.reactions=null:(r[s]=r[i],r.pop())}}r===null&&(t.f&Ye)!==0&&(Ie===null||!Ie.includes(t))&&(De(t,zt),(t.f&(Qe|Nr))===0&&(t.f^=Nr),ns(t),Lr(t,0))}function Lr(e,t){var r=e.deps;if(r!==null)for(var s=t;s<r.length;s++)Qn(e,r[s])}function Xt(e){var t=e.f;if((t&jt)===0){De(e,Ce);var r=re,s=Kt;re=e,Kt=!0;try{(t&wr)!==0?Hn(e):gs(e),hs(e);var i=ks(e);e.teardown=typeof i=="function"?i:null,e.wv=ws;var n;Xa&&Tn&&(e.f&Ze)!==0&&e.deps}finally{Kt=s,re=r}}}function a(e){var t=e.f,r=(t&Ye)!==0;if(Jt?.add(e),se!==null&&!ut){var s=re!==null&&(re.f&jt)!==0;if(!s&&!yt?.includes(e)){var i=se.deps;if((se.f&va)!==0)e.rv<br&&(e.rv=br,Ie===null&&i!==null&&i[We]===e?We++:Ie===null?Ie=[e]:(!Ct||!Ie.includes(e))&&Ie.push(e));else{(se.deps??=[]).push(e);var n=e.reactions;n===null?e.reactions=[se]:n.includes(se)||n.push(se)}}}else if(r&&e.deps===null&&e.effects===null){var l=e,v=l.parent;v!==null&&(v.f&Qe)===0&&(l.f^=Qe)}if($t){if(Ot.has(e))return Ot.get(e);if(r){l=e;var o=l.v;return((l.f&Ce)===0&&l.reactions!==null||Es(l))&&(o=Sa(l)),Ot.set(l,o),o}}else if(r){if(l=e,Zt?.has(l))return Zt.get(l);zr(l)&&is(l)}if((e.f&It)!==0)throw e.v;return e.v}function Es(e){if(e.v===Se)return!0;if(e.deps===null)return!1;for(const t of e.deps)if(Ot.has(t)||(t.f&Ye)!==0&&Es(t))return!0;return!1}function S(e){var t=ut;try{return ut=!0,e()}finally{ut=t}}const Zn=-7169;function De(e,t){e.f=e.f&Zn|t}function nt(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(bt in e)da(e);else if(!Array.isArray(e))for(let t in e){const r=e[t];typeof r=="object"&&r&&bt in r&&da(r)}}}function da(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let s in e)try{da(e[s],t)}catch{}const r=ga(e);if(r!==Object.prototype&&r!==Array.prototype&&r!==Map.prototype&&r!==Set.prototype&&r!==Date.prototype){const s=Qa(r);for(let i in s){const n=s[i].get;if(n)try{n.call(e)}catch{}}}}}const ei=["touchstart","touchmove"];function ti(e){return ei.includes(e)}let Ba=!1;function ri(){Ba||(Ba=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function Ss(e){var t=se,r=re;ft(null),At(null);try{return e()}finally{ft(t),At(r)}}function Cs(e,t,r,s=r){e.addEventListener(t,()=>Ss(r));const i=e.__on_r;i?e.__on_r=()=>{i(),s(!0)}:e.__on_r=()=>s(!0),ri()}const ai=new Set,Va=new Set;function si(e,t,r,s={}){function i(n){if(s.capture||_r.call(t,n),!n.cancelBubble)return Ss(()=>r?.call(this,n))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?Ea(()=>{t.addEventListener(e,i,s)}):t.addEventListener(e,i,s),i}function K(e,t,r,s,i){var n={capture:s,passive:i},l=si(e,t,r,n);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&Aa(()=>{t.removeEventListener(e,l,n)})}let Ga=null;function _r(e){var t=this,r=t.ownerDocument,s=e.type,i=e.composedPath?.()||[],n=i[0]||e.target;Ga=e;var l=0,v=Ga===e&&e.__root;if(v){var o=i.indexOf(v);if(o!==-1&&(t===document||t===window)){e.__root=t;return}var u=i.indexOf(t);if(u===-1)return;o<=u&&(l=o)}if(n=i[l]||e.target,n!==t){oa(e,"currentTarget",{configurable:!0,get(){return n||r}});var d=se,E=re;ft(null),At(null);try{for(var g,x=[];n!==null;){var m=n.assignedSlot||n.parentNode||n.host||null;try{var O=n["__"+s];if(O!=null&&(!n.disabled||e.target===n))if(qr(O)){var[P,...B]=O;P.apply(n,[e,...B])}else O.call(n,e)}catch(V){g?x.push(V):g=V}if(e.cancelBubble||m===t||m===null)break;n=m}if(g){for(let V of x)queueMicrotask(()=>{throw V});throw g}}finally{e.__root=t,delete e.currentTarget,ft(d),At(E)}}}function ni(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function jr(e,t){var r=re;r.nodes_start===null&&(r.nodes_start=e,r.nodes_end=t)}function N(e,t){var r=(t&xn)!==0,s=(t&zn)!==0,i,n=!e.startsWith("<!>");return()=>{i===void 0&&(i=ni(n?e:"<!>"+e),r||(i=Pr(i)));var l=s||vs?document.importNode(i,!0):i.cloneNode(!0);if(r){var v=Pr(l),o=l.lastChild;jr(v,o)}else jr(l,l);return l}}function mt(e=""){{var t=qt(e+"");return jr(t,t),t}}function ye(){var e=document.createDocumentFragment(),t=document.createComment(""),r=qt();return e.append(t,r),jr(t,r),e}function b(e,t){e!==null&&e.before(t)}function T(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??=e.nodeValue)&&(e.__t=r,e.nodeValue=r+"")}function ii(e,t){return li(e,t)}const Ht=new Map;function li(e,{target:t,anchor:r,props:s={},events:i,context:n,intro:l=!0}){Fn();var v=new Set,o=E=>{for(var g=0;g<E.length;g++){var x=E[g];if(!v.has(x)){v.add(x);var m=ti(x);t.addEventListener(x,_r,{passive:m});var O=Ht.get(x);O===void 0?(document.addEventListener(x,_r,{passive:m}),Ht.set(x,1)):Ht.set(x,O+1)}}};o(ha(ai)),Va.add(o);var u=void 0,d=Gn(()=>{var E=r??t.appendChild(qt());return rr(()=>{if(n){et({});var g=ge;g.c=n}i&&(s.$$events=i),u=e(E,s)||{},n&&tt()}),()=>{for(var g of v){t.removeEventListener(g,_r);var x=Ht.get(g);--x===0?(document.removeEventListener(g,_r),Ht.delete(g)):Ht.set(g,x)}Va.delete(o),E!==r&&E.parentNode?.removeChild(E)}});return oi.set(u,d),u}let oi=new WeakMap;function Q(e,t,r=!1){var s=e,i=null,n=null,l=Se,v=r?Fr:0,o=!1;const u=(x,m=!0)=>{o=!0,g(m,x)};var d=null;function E(){d!==null&&(d.lastChild.remove(),s.before(d),d=null);var x=l?i:n,m=l?n:i;x&&Na(x),m&&Hr(m,()=>{l?n=null:i=null})}const g=(x,m)=>{if(l!==(l=x)){var O=Ta(),P=s;if(O&&(d=document.createDocumentFragment(),d.append(P=qt())),l?i??=m&&rr(()=>m(P)):n??=m&&rr(()=>m(P)),O){var B=oe,V=l?i:n,w=l?n:i;V&&B.skipped_effects.delete(V),w&&B.skipped_effects.add(w),B.add_callback(E)}else E()}};Ra(()=>{o=!1,t(u),o||g(null,null)},v)}function $e(e,t){return t}function ci(e,t,r){for(var s=e.items,i=[],n=t.length,l=0;l<n;l++)Ma(t[l].e,i,!0);var v=n>0&&i.length===0&&r!==null;if(v){var o=r.parentNode;Un(o),o.append(r),s.clear(),vt(e,t[0].prev,t[n-1].next)}bs(i,()=>{for(var u=0;u<n;u++){var d=t[u];v||(s.delete(d.k),vt(e,d.prev,d.next)),wt(d.e,!v)}})}function Fe(e,t,r,s,i,n=null){var l=e,v={flags:t,items:new Map,first:null},o=(t&ts)!==0;if(o){var u=e;l=u.appendChild(qt())}var d=null,E=!1,g=new Map,x=Qt(()=>{var B=r();return qr(B)?B:B==null?[]:ha(B)}),m,O;function P(){vi(O,m,v,g,l,i,t,s,r),n!==null&&(m.length===0?d?Na(d):d=rr(()=>n(l)):d!==null&&Hr(d,()=>{d=null}))}Ra(()=>{O??=re,m=a(x);var B=m.length;if(!(E&&B===0)){E=B===0;var V,w,F,U;if(Ta()){var _=new Set,z=oe;for(w=0;w<B;w+=1){F=m[w],U=s(F,w);var j=v.items.get(U)??g.get(U);j?(t&(Ur|Br))!==0&&Ts(j,F,w,t):(V=As(null,v,null,null,F,U,w,i,t,r,!0),g.set(U,V)),_.add(U)}for(const[p,A]of v.items)_.has(p)||z.skipped_effects.add(A.e);z.add_callback(P)}else P();a(x)}})}function vi(e,t,r,s,i,n,l,v,o){var u=(l&_n)!==0,d=(l&(Ur|Br))!==0,E=t.length,g=r.items,x=r.first,m=x,O,P=null,B,V=[],w=[],F,U,_,z;if(u)for(z=0;z<E;z+=1)F=t[z],U=v(F,z),_=g.get(U),_!==void 0&&(_.a?.measure(),(B??=new Set).add(_));for(z=0;z<E;z+=1){if(F=t[z],U=v(F,z),_=g.get(U),_===void 0){var j=s.get(U);if(j!==void 0){s.delete(U),g.set(U,j);var p=P?P.next:m;vt(r,P,j),vt(r,j,p),sa(j,p,i),P=j}else{var A=m?m.e.nodes_start:i;P=As(A,r,P,P===null?r.first:P.next,F,U,z,n,l,o)}g.set(U,P),V=[],w=[],m=P.next;continue}if(d&&Ts(_,F,z,l),(_.e.f&it)!==0&&(Na(_.e),u&&(_.a?.unfix(),(B??=new Set).delete(_))),_!==m){if(O!==void 0&&O.has(_)){if(V.length<w.length){var q=w[0],I;P=q.prev;var L=V[0],$=V[V.length-1];for(I=0;I<V.length;I+=1)sa(V[I],q,i);for(I=0;I<w.length;I+=1)O.delete(w[I]);vt(r,L.prev,$.next),vt(r,P,L),vt(r,$,q),m=q,P=$,z-=1,V=[],w=[]}else O.delete(_),sa(_,m,i),vt(r,_.prev,_.next),vt(r,_,P===null?r.first:P.next),vt(r,P,_),P=_;continue}for(V=[],w=[];m!==null&&m.k!==U;)(m.e.f&it)===0&&(O??=new Set).add(m),w.push(m),m=m.next;if(m===null)continue;_=m}V.push(_),P=_,m=_.next}if(m!==null||O!==void 0){for(var C=O===void 0?[]:ha(O);m!==null;)(m.e.f&it)===0&&C.push(m),m=m.next;var k=C.length;if(k>0){var R=(l&ts)!==0&&E===0?i:null;if(u){for(z=0;z<k;z+=1)C[z].a?.measure();for(z=0;z<k;z+=1)C[z].a?.fix()}ci(r,C,R)}}u&&Ea(()=>{if(B!==void 0)for(_ of B)_.a?.apply()}),e.first=r.first&&r.first.e,e.last=P&&P.e;for(var y of s.values())wt(y.e);s.clear()}function Ts(e,t,r,s){(s&Ur)!==0&&tr(e.v,t),(s&Br)!==0?tr(e.i,r):e.i=r}function As(e,t,r,s,i,n,l,v,o,u,d){var E=(o&Ur)!==0,g=(o&hn)===0,x=E?g?X(i,!1,!1):mr(i):i,m=(o&Br)===0?l:mr(l),O={i:m,v:x,k:n,a:null,e:null,prev:r,next:s};try{if(e===null){var P=document.createDocumentFragment();P.append(e=qt())}return O.e=rr(()=>v(e,x,m,u),Sn),O.e.prev=r&&r.e,O.e.next=s&&s.e,r===null?d||(t.first=O):(r.next=O,r.e.next=O.e),s!==null&&(s.prev=O,s.e.prev=O.e),O}finally{}}function sa(e,t,r){for(var s=e.next?e.next.e.nodes_start:r,i=t?t.e.nodes_start:r,n=e.e.nodes_start;n!==null&&n!==s;){var l=Gr(n);i.before(n),n=l}}function vt(e,t,r){t===null?e.first=r:(t.next=r,t.e.next=r&&r.e),r!==null&&(r.prev=t,r.e.prev=t&&t.e)}function ui(e,t,r){var s=e,i,n,l=null,v=null;function o(){n&&(Hr(n),n=null),l&&(l.lastChild.remove(),s.before(l),l=null),n=v,v=null}Ra(()=>{if(i!==(i=t())){var u=Ta();if(i){var d=s;u&&(l=document.createDocumentFragment(),l.append(d=qt())),v=rr(()=>r(d,i))}u?oe.add_callback(o):o()}},Fr)}const Wa=[...` 	
\r\f \v\uFEFF`];function fi(e,t,r){var s=e==null?"":""+e;if(r){for(var i in r)if(r[i])s=s?s+" "+i:i;else if(s.length)for(var n=i.length,l=0;(l=s.indexOf(i,l))>=0;){var v=l+n;(l===0||Wa.includes(s[l-1]))&&(v===s.length||Wa.includes(s[v]))?s=(l===0?"":s.substring(0,l))+s.substring(v+1):l=v}}return s===""?null:s}function di(e,t){return e==null?null:String(e)}function st(e,t,r,s,i,n){var l=e.__className;if(l!==r||l===void 0){var v=fi(r,s,n);v==null?e.removeAttribute("class"):e.className=v,e.__className=r}else if(n&&i!==n)for(var o in n){var u=!!n[o];(i==null||u!==!!i[o])&&e.classList.toggle(o,u)}return n}function Je(e,t,r,s){var i=e.__style;if(i!==t){var n=di(t);n==null?e.removeAttribute("style"):e.style.cssText=n,e.__style=t}return s}function Rs(e,t,r=!1){if(e.multiple){if(t==null)return;if(!qr(t))return En();for(var s of e.options)s.selected=t.includes(hr(s));return}for(s of e.options){var i=hr(s);if($n(i,t)){s.selected=!0;return}}(!r||t!==void 0)&&(e.selectedIndex=-1)}function pi(e){var t=new MutationObserver(()=>{Rs(e,e.__value)});t.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),Aa(()=>{t.disconnect()})}function sr(e,t,r=t){var s=!0;Cs(e,"change",i=>{var n=i?"[selected]":":checked",l;if(e.multiple)l=[].map.call(e.querySelectorAll(n),hr);else{var v=e.querySelector(n)??e.querySelector("option:not([disabled])");l=v&&hr(v)}r(l)}),_s(()=>{var i=t();if(Rs(e,i,s),s&&i===void 0){var n=e.querySelector(":checked");n!==null&&(i=hr(n),r(i))}e.__value=i,s=!1}),pi(e)}function hr(e){return"__value"in e?e.__value:e.value}const _i=Symbol("is custom element"),hi=Symbol("is html");function Ms(e,t,r,s){var i=gi(e);i[t]!==(i[t]=r)&&(r==null?e.removeAttribute(t):typeof r!="string"&&mi(e).includes(t)?e[t]=r:e.setAttribute(t,r))}function gi(e){return e.__attributes??={[_i]:e.nodeName.includes("-"),[hi]:e.namespaceURI===kn}}var Ha=new Map;function mi(e){var t=Ha.get(e.nodeName);if(t)return t;Ha.set(e.nodeName,t=[]);for(var r,s=e,i=Element.prototype;i!==s;){r=Qa(s);for(var n in r)r[n].set&&t.push(n);s=ga(s)}return t}function yr(e,t,r=t){var s=ir(),i=new WeakSet;Cs(e,"input",n=>{var l=n?e.defaultValue:e.value;if(l=na(e)?ia(l):l,r(l),oe!==null&&i.add(oe),s&&l!==(l=t())){var v=e.selectionStart,o=e.selectionEnd;e.value=l??"",o!==null&&(e.selectionStart=v,e.selectionEnd=Math.min(o,e.value.length))}}),S(t)==null&&e.value&&(r(na(e)?ia(e.value):e.value),oe!==null&&i.add(oe)),Wr(()=>{var n=t();if(e===document.activeElement){var l=Mr??oe;if(i.has(l))return}na(e)&&n===ia(e.value)||e.type==="date"&&!n&&!e.value||n!==e.value&&(e.value=n??"")})}function na(e){var t=e.type;return t==="number"||t==="range"}function ia(e){return e===""?null:+e}function pr(e,t,r){var s=Nt(e,t);s&&s.set&&(e[t]=r,Aa(()=>{e[t]=null}))}function Ja(e,t){return e===t||e?.[bt]===t}function Ns(e={},t,r,s){return _s(()=>{var i,n;return Wr(()=>{i=n,n=[],S(()=>{e!==r(...n)&&(t(e,...n),i&&Ja(r(...i),e)&&t(null,...i))})}),()=>{Ea(()=>{n&&Ja(r(...n),e)&&t(null,...n)})}}),e}function bi(e){return function(...t){var r=t[0];return r.stopPropagation(),e?.apply(this,t)}}function lt(e=!1){const t=ge,r=t.l.u;if(!r)return;let s=()=>nt(t.s);if(e){let i=0,n={};const l=Vr(()=>{let v=!1;const o=t.s;for(const u in o)o[u]!==n[u]&&(n[u]=o[u],v=!0);return v&&i++,i});s=()=>a(l)}r.b.length&&Vn(()=>{Ya(t,s),ca(r.b)}),fa(()=>{const i=S(()=>r.m.map(en));return()=>{for(const n of i)typeof n=="function"&&n()}}),r.a.length&&fa(()=>{Ya(t,s),ca(r.a)})}function Ya(e,t){if(e.l.s)for(const r of e.l.s)a(r);t()}let Rr=!1;function yi(e){var t=Rr;try{return Rr=!1,[e(),Rr]}finally{Rr=t}}function Oe(e,t,r,s){var i=!nr||(r&mn)!==0,n=(r&yn)!==0,l=(r&wn)!==0,v=s,o=!0,u=()=>(o&&(o=!1,v=l?S(s):s),v),d;if(n){var E=bt in e||an in e;d=Nt(e,t)?.set??(E&&t in e?w=>e[t]=w:void 0)}var g,x=!1;n?[g,x]=yi(()=>e[t]):g=e[t],g===void 0&&s!==void 0&&(g=u(),d&&(i&&un(),d(g)));var m;if(i?m=()=>{var w=e[t];return w===void 0?u():(o=!0,w)}:m=()=>{var w=e[t];return w!==void 0&&(v=void 0),w===void 0?v:w},i&&(r&bn)===0)return m;if(d){var O=e.$$legacy;return function(w,F){return arguments.length>0?((!i||!F||O||x)&&d(F?m():w),w):m()}}var P=!1,B=((r&gn)!==0?Vr:Qt)(()=>(P=!1,m()));n&&a(B);var V=re;return function(w,F){if(arguments.length>0){const U=F?a(B):i&&n?Yt(w):w;return h(B,U),P=!0,v!==void 0&&(v=U),w}return $t&&P||(V.f&jt)!==0?B.v:a(B)}}function pt(e){ge===null&&es(),nr&&ge.l!==null?wi(ge).m.push(e):fa(()=>{const t=S(e);if(typeof t=="function")return t})}function Is(e){ge===null&&es(),pt(()=>()=>S(e))}function wi(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}const xi="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(xi);An();class zi{routes=new Map;currentRoute="/";onRouteChange;constructor(){window.addEventListener("popstate",()=>{this.handleLocationChange()})}register(t,r){this.routes.set(t,r)}navigate(t){t!==this.currentRoute&&(this.currentRoute=t,window.history.pushState({},"",t),this.handleLocationChange())}getCurrentRoute(){return this.currentRoute}setOnRouteChange(t){this.onRouteChange=t}handleLocationChange(){const t=window.location.pathname;this.currentRoute=t;const r=this.findMatchingRoute(t);if(r){const{routePattern:s,params:i}=r,n=this.routes.get(s);n&&n(i)}this.onRouteChange&&this.onRouteChange(t)}findMatchingRoute(t){for(const r of this.routes.keys()){const s=this.matchRoute(r,t);if(s!==null)return{routePattern:r,params:s}}return null}matchRoute(t,r){const s=t.split("/"),i=r.split("/");if(s.length!==i.length)return null;const n={};for(let l=0;l<s.length;l++){const v=s[l],o=i[l];if(v.startsWith(":")){const u=v.slice(1);n[u]=o}else if(v!==o)return null}return n}init(){this.handleLocationChange()}}const He=new zi;var ki=N('<div class="mobile-menu svelte-uax3le"><button><span class="nav-icon svelte-uax3le">🏠</span> 首页</button> <button><span class="nav-icon svelte-uax3le">🔍</span> 算法可视化</button> <button><span class="nav-icon svelte-uax3le">📝</span> 题目练习</button> <button><span class="nav-icon svelte-uax3le">📊</span> 提交记录</button></div>'),Ei=N('<nav class="navigation svelte-uax3le"><div class="nav-container svelte-uax3le"><div class="nav-brand svelte-uax3le"><button class="brand-link svelte-uax3le"><span class="brand-icon svelte-uax3le">🏟️</span> <span class="brand-text svelte-uax3le">算法竞技场</span></button></div> <div class="nav-menu svelte-uax3le"><button><span class="nav-icon svelte-uax3le">🏠</span> 首页</button> <button><span class="nav-icon svelte-uax3le">🔍</span> 算法可视化</button> <button><span class="nav-icon svelte-uax3le">📝</span> 题目练习</button> <button><span class="nav-icon svelte-uax3le">📊</span> 提交记录</button></div> <button class="mobile-menu-btn svelte-uax3le"><span><span class="svelte-uax3le"></span> <span class="svelte-uax3le"></span> <span class="svelte-uax3le"></span></span></button></div> <!></nav>');function Si(e,t){et(t,!1);let r="/",s=X(!1);function i(p){He.navigate(p),h(s,!1)}function n(){h(s,!a(s))}function l(p){return p==="/"?r==="/":r.startsWith(p)}He.subscribe(p=>{r=p}),lt();var v=Ei(),o=c(v),u=c(o),d=c(u),E=f(u,2),g=c(E);let x;var m=f(g,2);let O;var P=f(m,2);let B;var V=f(P,2);let w;var F=f(E,2),U=c(F);let _;var z=f(o,2);{var j=p=>{var A=ki(),q=c(A);let I;var L=f(q,2);let $;var C=f(L,2);let k;var R=f(C,2);let y;Y((M,W,J,D)=>{I=st(q,1,"mobile-nav-link svelte-uax3le",null,I,M),$=st(L,1,"mobile-nav-link svelte-uax3le",null,$,W),k=st(C,1,"mobile-nav-link svelte-uax3le",null,k,J),y=st(R,1,"mobile-nav-link svelte-uax3le",null,y,D)},[()=>({active:l("/")}),()=>({active:l("/algorithms")}),()=>({active:l("/problems")}),()=>({active:l("/submissions")})]),K("click",q,()=>i("/")),K("click",L,()=>i("/algorithms")),K("click",C,()=>i("/problems")),K("click",R,()=>i("/submissions")),b(p,A)};Q(z,p=>{a(s)&&p(j)})}Y((p,A,q,I,L)=>{x=st(g,1,"nav-link svelte-uax3le",null,x,p),O=st(m,1,"nav-link svelte-uax3le",null,O,A),B=st(P,1,"nav-link svelte-uax3le",null,B,q),w=st(V,1,"nav-link svelte-uax3le",null,w,I),_=st(U,1,"hamburger svelte-uax3le",null,_,L)},[()=>({active:l("/")}),()=>({active:l("/algorithms")}),()=>({active:l("/problems")}),()=>({active:l("/submissions")}),()=>({open:a(s)})]),K("click",d,()=>i("/")),K("click",g,()=>i("/")),K("click",m,()=>i("/algorithms")),K("click",P,()=>i("/problems")),K("click",V,()=>i("/submissions")),K("click",F,n),b(e,v),tt()}var Ci=N('<div class="demo-bar svelte-4gzr7k"><span class="bar-value svelte-4gzr7k"> </span></div>'),Ti=N('<div class="feature-card svelte-4gzr7k"><div class="feature-icon svelte-4gzr7k"> </div> <h3 class="svelte-4gzr7k"> </h3> <p class="svelte-4gzr7k"> </p> <button class="feature-button svelte-4gzr7k">立即体验</button></div>'),Ai=N('<div class="algorithm-item svelte-4gzr7k"><div class="algorithm-info svelte-4gzr7k"><h4 class="svelte-4gzr7k"> </h4> <span class="algorithm-category svelte-4gzr7k"> </span></div> <span class="difficulty-badge svelte-4gzr7k"> </span></div>'),Ri=N('<div class="problem-item svelte-4gzr7k"><div class="problem-info svelte-4gzr7k"><h4 class="svelte-4gzr7k"> </h4> <span class="problem-category svelte-4gzr7k"> </span></div> <span class="difficulty-badge svelte-4gzr7k"> </span></div>'),Mi=N('<div class="home-page svelte-4gzr7k"><section class="hero svelte-4gzr7k"><div class="hero-content svelte-4gzr7k"><h1 class="hero-title svelte-4gzr7k">算法竞技场</h1> <p class="hero-subtitle svelte-4gzr7k">通过可视化学习算法，在线编程练习，提升你的编程技能</p> <div class="hero-actions svelte-4gzr7k"><button class="cta-button primary svelte-4gzr7k">开始学习</button> <button class="cta-button secondary svelte-4gzr7k">开始练习</button></div></div> <div class="hero-visual svelte-4gzr7k"><div class="algorithm-demo svelte-4gzr7k"><div class="demo-bars svelte-4gzr7k"></div> <p class="demo-label svelte-4gzr7k">排序算法可视化演示</p></div></div></section> <section class="features svelte-4gzr7k"><div class="section-header svelte-4gzr7k"><h2 class="svelte-4gzr7k">核心功能</h2> <p class="svelte-4gzr7k">全方位的算法学习和编程练习平台</p></div> <div class="features-grid svelte-4gzr7k"></div></section> <section class="popular-algorithms svelte-4gzr7k"><div class="section-header svelte-4gzr7k"><h2 class="svelte-4gzr7k">热门算法</h2> <p class="svelte-4gzr7k">从基础到进阶，掌握经典算法</p></div> <div class="algorithms-grid svelte-4gzr7k"></div> <div class="section-footer svelte-4gzr7k"><button class="view-all-button svelte-4gzr7k">查看所有算法 →</button></div></section> <section class="popular-problems svelte-4gzr7k"><div class="section-header svelte-4gzr7k"><h2 class="svelte-4gzr7k">热门题目</h2> <p class="svelte-4gzr7k">挑战经典编程问题，提升解题能力</p></div> <div class="problems-grid svelte-4gzr7k"></div> <div class="section-footer svelte-4gzr7k"><button class="view-all-button svelte-4gzr7k">查看所有题目 →</button></div></section> <section class="statistics svelte-4gzr7k"><div class="stats-grid svelte-4gzr7k"><div class="stat-item svelte-4gzr7k"><div class="stat-number svelte-4gzr7k">20+</div> <div class="stat-label svelte-4gzr7k">算法可视化</div></div> <div class="stat-item svelte-4gzr7k"><div class="stat-number svelte-4gzr7k">100+</div> <div class="stat-label svelte-4gzr7k">编程题目</div></div> <div class="stat-item svelte-4gzr7k"><div class="stat-number svelte-4gzr7k">3</div> <div class="stat-label svelte-4gzr7k">编程语言</div></div> <div class="stat-item svelte-4gzr7k"><div class="stat-number svelte-4gzr7k">∞</div> <div class="stat-label svelte-4gzr7k">学习可能</div></div></div></section></div>');function Ka(e,t){et(t,!1);function r(q){He.navigate(q)}const s=[{icon:"🔍",title:"算法可视化",description:"通过动画演示理解排序、搜索等经典算法的工作原理",path:"/algorithms",color:"#3b82f6"},{icon:"📝",title:"在线编程",description:"挑战各种算法题目，支持多种编程语言在线编辑和测试",path:"/problems",color:"#10b981"},{icon:"📊",title:"性能分析",description:"详细的代码执行分析，包括时间复杂度和空间复杂度统计",path:"/submissions",color:"#f59e0b"}],i=[{name:"冒泡排序",category:"sorting",difficulty:"easy"},{name:"快速排序",category:"sorting",difficulty:"medium"},{name:"归并排序",category:"sorting",difficulty:"medium"},{name:"二分查找",category:"searching",difficulty:"easy"},{name:"深度优先搜索",category:"graph",difficulty:"hard"},{name:"广度优先搜索",category:"graph",difficulty:"hard"}],n=[{title:"两数之和",difficulty:"easy",category:"array"},{title:"反转链表",difficulty:"easy",category:"linked-list"},{title:"最大子数组和",difficulty:"medium",category:"array"},{title:"二叉树的中序遍历",difficulty:"medium",category:"tree"},{title:"最长公共子序列",difficulty:"hard",category:"dynamic"},{title:"图的最短路径",difficulty:"hard",category:"graph"}];function l(q){return{easy:"#10b981",medium:"#f59e0b",hard:"#ef4444"}[q]||"#6b7280"}function v(q){return{easy:"简单",medium:"中等",hard:"困难"}[q]||q}lt();var o=Mi(),u=c(o),d=c(u),E=f(c(d),4),g=c(E),x=f(g,2),m=f(d,2),O=c(m),P=c(O);Fe(P,4,()=>[64,34,25,12,22,11,90],$e,(q,I,L)=>{var $=Ci(),C=c($),k=c(C);Y(()=>{Je($,`height: ${I??""}px; animation-delay: ${L*.1}s;`),T(k,I)}),b(q,$)});var B=f(u,2),V=f(c(B),2);Fe(V,5,()=>s,$e,(q,I)=>{var L=Ti(),$=c(L),C=c($),k=f($,2),R=c(k),y=f(k,2),M=c(y),W=f(y,2);Y(()=>{Je($,`background-color: ${a(I).color??""}20; color: ${a(I).color??""}`),T(C,a(I).icon),T(R,a(I).title),T(M,a(I).description),Je(W,`background-color: ${a(I).color??""}`)}),K("click",L,()=>r(a(I).path)),b(q,L)});var w=f(B,2),F=f(c(w),2);Fe(F,5,()=>i,$e,(q,I)=>{var L=Ai(),$=c(L),C=c($),k=c(C),R=f(C,2),y=c(R),M=f($,2),W=c(M);Y((J,D)=>{T(k,a(I).name),T(y,a(I).category),Je(M,`background-color: ${J??""}`),T(W,D)},[()=>l(a(I).difficulty),()=>v(a(I).difficulty)]),K("click",L,()=>r("/algorithms")),b(q,L)});var U=f(F,2),_=c(U),z=f(w,2),j=f(c(z),2);Fe(j,5,()=>n,$e,(q,I)=>{var L=Ri(),$=c(L),C=c($),k=c(C),R=f(C,2),y=c(R),M=f($,2),W=c(M);Y((J,D)=>{T(k,a(I).title),T(y,a(I).category),Je(M,`background-color: ${J??""}`),T(W,D)},[()=>l(a(I).difficulty),()=>v(a(I).difficulty)]),K("click",L,()=>r("/problems")),b(q,L)});var p=f(j,2),A=c(p);K("click",g,()=>r("/algorithms")),K("click",x,()=>r("/problems")),K("click",_,()=>r("/algorithms")),K("click",A,()=>r("/problems")),b(e,o),tt()}const Ni="http://localhost:8080/api";class Ii{baseUrl;constructor(t=Ni){this.baseUrl=t}async get(t,r){const s=new URL(`${this.baseUrl}${t}`);r&&Object.entries(r).forEach(([n,l])=>{l&&s.searchParams.append(n,l)});const i=await fetch(s.toString());if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);return await i.json()}async post(t,r){const s=await fetch(`${this.baseUrl}${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:r?JSON.stringify(r):void 0});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);return await s.json()}async put(t,r){const s=await fetch(`${this.baseUrl}${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!s.ok)throw new Error(`HTTP error! status: ${s.status}`);return await s.json()}async delete(t){const r=await fetch(`${this.baseUrl}${t}`,{method:"DELETE"});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`)}}const de=new Ii;class Oi{static async getAll(t,r){const s={};return t&&(s.category=t),r&&(s.name=r),de.get("/algorithms",s)}static async getById(t){return de.get(`/algorithms/${t}`)}static async getCategories(){return de.get("/algorithms/categories")}static async create(t){return de.post("/algorithms",t)}static async update(t,r){return de.put(`/algorithms/${t}`,r)}static async delete(t){return de.delete(`/algorithms/${t}`)}}class Di{static async getAll(t,r,s){const i={};return t&&(i.difficulty=t),r&&(i.category=r),s&&(i.title=s),de.get("/problems",i)}static async getById(t){return de.get(`/problems/${t}`)}static async getCategories(){return de.get("/problems/categories")}static async getByDifficulty(t){return de.get(`/problems/difficulty/${t}`)}static async create(t){return de.post("/problems",t)}static async update(t,r){return de.put(`/problems/${t}`,r)}static async delete(t){return de.delete(`/problems/${t}`)}}class Pi{static async getAll(t,r){const s={};return t&&(s.userId=t),r&&(s.problemId=r.toString()),de.get("/submissions",s)}static async getById(t){return de.get(`/submissions/${t}`)}static async submit(t){return de.post("/submissions",t)}static async getUserStats(t){return de.get(`/submissions/stats/${t}`)}static async getProblemAcceptanceRate(t){return de.get(`/submissions/problems/${t}/acceptance-rate`)}}class Li{static async generateBubbleSort(t){const r=await de.post("/visualization/bubble-sort",{array:t});return JSON.parse(r)}static async generateQuickSort(t){const r=await de.post("/visualization/quick-sort",{array:t});return JSON.parse(r)}static async generateBinarySearch(t,r){const s=await de.post("/visualization/binary-search",{array:t,target:r});return JSON.parse(s)}static async getSupportedAlgorithms(){const t=await de.get("/visualization/algorithms");return JSON.parse(t)}static async generateRandomArray(t=10,r=1,s=100){const i=await de.get("/visualization/random-array",{size:t.toString(),min:r.toString(),max:s.toString()});return JSON.parse(i).array}static async generateSortedArray(t=10,r=1,s=2){const i=await de.get("/visualization/sorted-array",{size:t.toString(),start:r.toString(),step:s.toString()});return JSON.parse(i).array}}const pa=Oi,Os=Di,_a=Pi,la=Li;var ji=N('<option class="svelte-l0pz0f"> </option>'),qi=N('<div class="loading svelte-l0pz0f"><div class="spinner svelte-l0pz0f"></div> <p class="svelte-l0pz0f">加载中...</p></div>'),$i=N('<div class="error svelte-l0pz0f"><p class="svelte-l0pz0f"> </p> <button class="retry-btn svelte-l0pz0f">重试</button></div>'),Fi=N('<div class="empty svelte-l0pz0f"><p class="svelte-l0pz0f">没有找到匹配的算法</p></div>'),Ui=N('<div class="algorithm-card svelte-l0pz0f"><div class="card-header svelte-l0pz0f"><h3 class="svelte-l0pz0f"> </h3> <span class="category-badge svelte-l0pz0f"> </span></div> <div class="card-content svelte-l0pz0f"><p class="description svelte-l0pz0f"> </p> <div class="complexity svelte-l0pz0f"><div class="complexity-item svelte-l0pz0f"><span class="label svelte-l0pz0f">时间复杂度:</span> <span class="value svelte-l0pz0f"> </span></div> <div class="complexity-item svelte-l0pz0f"><span class="label svelte-l0pz0f">空间复杂度:</span> <span class="value svelte-l0pz0f"> </span></div></div></div> <div class="card-footer svelte-l0pz0f"><button class="view-btn svelte-l0pz0f">查看可视化</button></div></div>'),Bi=N('<div class="algorithm-grid svelte-l0pz0f"></div>'),Vi=N('<div class="algorithm-list svelte-l0pz0f"><div class="header svelte-l0pz0f"><h1 class="svelte-l0pz0f">算法可视化</h1> <p class="svelte-l0pz0f">探索各种经典算法的工作原理</p></div> <div class="filters svelte-l0pz0f"><div class="search-box svelte-l0pz0f"><input type="text" placeholder="搜索算法..." class="search-input svelte-l0pz0f"/></div> <div class="category-filter svelte-l0pz0f"><select class="category-select svelte-l0pz0f"><option class="svelte-l0pz0f">所有分类</option><!></select></div></div> <!></div>');function Gi(e,t){et(t,!1);let r=X([]),s=X([]),i=X(""),n=X(""),l=X(!0),v=X("");async function o(){try{h(l,!0),h(v,"");const p={};a(i)&&(p.category=a(i)),a(n)&&(p.name=a(n)),h(r,await pa.getAll(p.category,p.name))}catch(p){h(v,"加载算法列表失败"),console.error("Failed to load algorithms:",p)}finally{h(l,!1)}}async function u(){try{h(s,await pa.getCategories())}catch(p){console.error("Failed to load categories:",p)}}function d(p){He.navigate(`/algorithms/${p.id}`)}function E(){o()}function g(){o()}function x(p){return{sorting:"#3b82f6",searching:"#10b981",tree:"#8b5cf6",graph:"#f59e0b",dynamic:"#ef4444",greedy:"#06b6d4"}[p]||"#6b7280"}pt(()=>{u(),o()}),lt();var m=Vi(),O=f(c(m),2),P=c(O),B=c(P),V=f(P,2),w=c(V);Y(()=>{a(i),ar(()=>{a(s)})});var F=c(w);F.value=F.__value="";var U=f(F);Fe(U,1,()=>a(s),$e,(p,A)=>{var q=ji(),I=c(q),L={};Y(()=>{T(I,a(A)),L!==(L=a(A))&&(q.value=(q.__value=a(A))??"")}),b(p,q)});var _=f(O,2);{var z=p=>{var A=qi();b(p,A)},j=p=>{var A=ye(),q=ve(A);{var I=$=>{var C=$i(),k=c(C),R=c(k),y=f(k,2);Y(()=>T(R,a(v))),K("click",y,o),b($,C)},L=$=>{var C=ye(),k=ve(C);{var R=M=>{var W=Fi();b(M,W)},y=M=>{var W=Bi();Fe(W,5,()=>a(r),$e,(J,D)=>{var H=Ui(),ee=c(H),G=c(ee),ie=c(G),pe=f(G,2),ue=c(pe),Z=f(ee,2),ce=c(Z),_e=c(ce),le=f(ce,2),Ae=c(le),Ke=f(c(Ae),2),we=c(Ke),te=f(Ae,2),Ee=f(c(te),2),Re=c(Ee);Y(Me=>{T(ie,a(D).name),Je(pe,`background-color: ${Me??""}`),T(ue,a(D).category),T(_e,a(D).description||"暂无描述"),T(we,a(D).timeComplexity),T(Re,a(D).spaceComplexity)},[()=>x(a(D).category)]),K("click",H,()=>d(a(D))),b(J,H)}),b(M,W)};Q(k,M=>{a(r).length===0?M(R):M(y,!1)},!0)}b($,C)};Q(q,$=>{a(v)?$(I):$(L,!1)},!0)}b(p,A)};Q(_,p=>{a(l)?p(z):p(j,!1)})}yr(B,()=>a(n),p=>h(n,p)),K("input",B,E),sr(w,()=>a(i),p=>h(i,p)),K("change",w,g),b(e,m),tt()}var Wi=N('<div class="d3-visualizer svelte-fyd4k6"><div class="svg-container svelte-fyd4k6"></div></div>');function Hi(e,t){et(t,!1);let r=Oe(t,"steps",24,()=>[]),s=Oe(t,"currentStep",8,0),i=Oe(t,"width",8,800),n=Oe(t,"height",8,400),l=Oe(t,"algorithmType",8,"sorting"),v=X(),o=X(),u=500;function d(){if(!a(v))return;ea(a(v)).selectAll("*").remove(),h(o,ea(a(v)).append("svg").attr("width",i()).attr("height",n()).attr("viewBox",`0 0 ${i()} ${n()}`).style("background","#f8fafc").style("border-radius","8px"));const _=a(o).append("defs"),z=_.append("linearGradient").attr("id","compare-gradient").attr("gradientUnits","userSpaceOnUse").attr("x1","0%").attr("y1","0%").attr("x2","0%").attr("y2","100%");z.append("stop").attr("offset","0%").attr("stop-color","#fbbf24").attr("stop-opacity",1),z.append("stop").attr("offset","100%").attr("stop-color","#f59e0b").attr("stop-opacity",1);const j=_.append("linearGradient").attr("id","swap-gradient").attr("gradientUnits","userSpaceOnUse").attr("x1","0%").attr("y1","0%").attr("x2","0%").attr("y2","100%");j.append("stop").attr("offset","0%").attr("stop-color","#f87171").attr("stop-opacity",1),j.append("stop").attr("offset","100%").attr("stop-color","#ef4444").attr("stop-opacity",1);const p=_.append("linearGradient").attr("id","sorted-gradient").attr("gradientUnits","userSpaceOnUse").attr("x1","0%").attr("y1","0%").attr("x2","0%").attr("y2","100%");p.append("stop").attr("offset","0%").attr("stop-color","#34d399").attr("stop-opacity",1),p.append("stop").attr("offset","100%").attr("stop-color","#10b981").attr("stop-opacity",1)}function E(){if(!a(o)||!r()[s()])return;const _=r()[s()],z=_.array||[],j=Math.max(...z),p=Math.min(60,(i()-100)/z.length),A=5,q=n()-120;a(o).selectAll(".bar-group").remove();const I=a(o).selectAll(".bar-group").data(z).enter().append("g").attr("class","bar-group").attr("transform",(L,$)=>`translate(${50+$*(p+A)}, 20)`);I.append("rect").attr("class","bar").attr("x",0).attr("y",L=>q-L/j*q+20).attr("width",p).attr("height",L=>L/j*q).attr("fill",(L,$)=>g($,_)).attr("stroke","#ffffff").attr("stroke-width",2).attr("rx",4).attr("ry",4),I.append("text").attr("class","bar-value").attr("x",p/2).attr("y",L=>q-L/j*q+15).attr("text-anchor","middle").attr("fill","#ffffff").attr("font-size","12px").attr("font-weight","bold").text(L=>L),I.append("text").attr("class","bar-index").attr("x",p/2).attr("y",q+45).attr("text-anchor","middle").attr("fill","#64748b").attr("font-size","10px").text((L,$)=>$),_.action==="swap"&&_.positions?x(_.positions):_.action==="compare"&&_.positions&&m(_.positions)}function g(_,z){if(z.positions&&z.positions.includes(_)){if(z.action==="compare"||z.action==="compare_with_pivot")return"url(#compare-gradient)";if(z.action==="swap"||z.action==="place_pivot")return"url(#swap-gradient)"}return z.action==="round_complete"&&_===z.sorted_position||z.action==="complete"?"url(#sorted-gradient)":"#64748b"}function x(_){if(_.length!==2)return;const[z,j]=_,p=Math.min(60,(i()-100)/r()[s()].array.length),A=5,q=a(o).select(`.bar-group:nth-child(${z+1})`),I=a(o).select(`.bar-group:nth-child(${j+1})`),L=50+z*(p+A),$=50+j*(p+A);q.transition().duration(u).attr("transform",`translate(${$}, 20)`).transition().duration(0).attr("transform",`translate(${L}, 20)`),I.transition().duration(u).attr("transform",`translate(${L}, 20)`).transition().duration(0).attr("transform",`translate(${$}, 20)`)}function m(_){_.forEach(z=>{a(o).select(`.bar-group:nth-child(${z+1}) .bar`).transition().duration(u/2).attr("transform","scale(1.1)").transition().duration(u/2).attr("transform","scale(1)")})}function O(){if(!a(o)||!r()[s()])return;const _=r()[s()],z=_.array||[],j=Math.min(60,(i()-100)/z.length),p=50,A=(i()-z.length*j)/2,q=n()/2-p/2;a(o).selectAll(".search-cell").remove(),a(o).selectAll(".pointer").remove();const I=a(o).selectAll(".search-cell").data(z).enter().append("g").attr("class","search-cell").attr("transform",(L,$)=>`translate(${A+$*j}, ${q})`);I.append("rect").attr("width",j-2).attr("height",p).attr("fill",(L,$)=>P($,_)).attr("stroke","#e2e8f0").attr("stroke-width",2).attr("rx",4),I.append("text").attr("x",j/2).attr("y",p/2+5).attr("text-anchor","middle").attr("fill","#ffffff").attr("font-size","14px").attr("font-weight","bold").text(L=>L),I.append("text").attr("x",j/2).attr("y",-10).attr("text-anchor","middle").attr("fill","#64748b").attr("font-size","10px").text((L,$)=>$),_.left!==void 0&&B(A+_.left*j+j/2,q-30,"L","#3b82f6"),_.right!==void 0&&B(A+_.right*j+j/2,q-30,"R","#ef4444"),_.mid!==void 0&&B(A+_.mid*j+j/2,q+p+20,"M","#10b981")}function P(_,z){return z.found_index===_?"#10b981":z.mid===_?"#f59e0b":z.left!==void 0&&z.right!==void 0&&_>=z.left&&_<=z.right?"#64748b":"#cbd5e1"}function B(_,z,j,p){const A=a(o).append("g").attr("class","pointer").attr("transform",`translate(${_}, ${z})`);A.append("circle").attr("r",12).attr("fill",p),A.append("text").attr("text-anchor","middle").attr("dy",4).attr("fill","#ffffff").attr("font-size","10px").attr("font-weight","bold").text(j)}function V(){if(!a(o)||!r()[s()])return;const _=r()[s()],z=_.nodes||[],j=_.edges||[];a(o).selectAll(".graph-node").remove(),a(o).selectAll(".graph-edge").remove(),a(o).selectAll(".graph-edge").data(j).enter().append("line").attr("class","graph-edge").attr("x1",A=>z[A.from]?.x||0).attr("y1",A=>z[A.from]?.y||0).attr("x2",A=>z[A.to]?.x||0).attr("y2",A=>z[A.to]?.y||0).attr("stroke","#e2e8f0").attr("stroke-width",2);const p=a(o).selectAll(".graph-node").data(z).enter().append("g").attr("class","graph-node").attr("transform",A=>`translate(${A.x}, ${A.y})`);if(p.append("circle").attr("r",20).attr("fill",A=>_.current===A.id?"#3b82f6":A.visited?"#10b981":"#64748b").attr("stroke","#ffffff").attr("stroke-width",2),p.append("text").attr("text-anchor","middle").attr("dy",5).attr("fill","#ffffff").attr("font-size","14px").attr("font-weight","bold").text(A=>A.id),_.stack||_.queue){const A=_.stack?_.stack:_.queue,q=_.stack?"栈":"队列";a(o).selectAll(".container-display").remove(),a(o).append("g").attr("class","container-display").attr("transform",`translate(${i()-150}, 50)`).append("text").attr("x",0).attr("y",-10).attr("font-size","14px").attr("font-weight","bold").attr("fill","#1e293b").text(`${q}: [${A.join(", ")}]`)}}function w(){if(!(!a(o)||r().length===0))switch(l()){case"sorting":E();break;case"searching":O();break;case"graph":V();break;default:E()}}pt(()=>{d(),w()}),Is(()=>{a(v)&&ea(a(v)).selectAll("*").remove()}),Tt(()=>(a(o),nt(r())),()=>{a(o)&&r().length>0&&w()}),Tt(()=>(a(o),nt(i()),nt(n())),()=>{a(o)&&(a(o).attr("width",i()).attr("height",n()),w())}),xr(),lt();var F=Wi(),U=c(F);Ns(U,_=>h(v,_),()=>a(v)),b(e,F),tt()}var Ji=N('<div class="loading svelte-1qwz5em"><div class="spinner svelte-1qwz5em"></div> <p class="svelte-1qwz5em">加载中...</p></div>'),Yi=N('<div class="error svelte-1qwz5em"><p class="svelte-1qwz5em"> </p> <button class="retry-btn svelte-1qwz5em">重试</button></div>'),Ki=N('<div class="step-info svelte-1qwz5em"><h3 class="svelte-1qwz5em"> </h3> <p class="step-description svelte-1qwz5em"><!></p></div>'),Xi=N('<div class="visualization-area svelte-1qwz5em"><!> <!> <div class="controls svelte-1qwz5em"><button class="control-btn svelte-1qwz5em" title="重置">⏮</button> <button class="control-btn svelte-1qwz5em" title="上一步">⏪</button> <button class="control-btn play-btn svelte-1qwz5em"> </button> <button class="control-btn svelte-1qwz5em" title="下一步">⏩</button> <div class="speed-control svelte-1qwz5em"><label class="svelte-1qwz5em">速度: <input type="range" min="100" max="2000" step="100" class="speed-slider svelte-1qwz5em"/> <span class="svelte-1qwz5em"> </span></label></div></div></div>'),Qi=N('<div class="no-visualization svelte-1qwz5em"><p class="svelte-1qwz5em">点击"应用"按钮生成可视化</p></div>'),Zi=N('<div class="algorithm-header svelte-1qwz5em"><h1 class="svelte-1qwz5em"> </h1> <p class="svelte-1qwz5em"> </p> <div class="complexity-info svelte-1qwz5em"><span class="svelte-1qwz5em">时间复杂度: <code class="svelte-1qwz5em"> </code></span> <span class="svelte-1qwz5em">空间复杂度: <code class="svelte-1qwz5em"> </code></span></div></div> <div class="input-section svelte-1qwz5em"><div class="input-controls svelte-1qwz5em"><label class="svelte-1qwz5em">自定义数组 (用逗号分隔): <input type="text" placeholder="64, 34, 25, 12, 22, 11, 90" class="array-input svelte-1qwz5em"/></label> <button class="apply-btn svelte-1qwz5em">应用</button> <button class="random-btn svelte-1qwz5em">随机生成</button></div></div> <!>',1),el=N('<div class="visualizer svelte-1qwz5em"><!></div>');function tl(e,t){et(t,!1);const r=X();let s=Oe(t,"params",24,()=>({})),i=X(null),n=X(null),l=X(0),v=X(!1),o=X(1e3),u=[64,34,25,12,22,11,90],d=X(""),E=X(!0),g=X(""),x=null;async function m(){if(s().id)try{h(E,!0),h(g,""),h(i,await pa.getById(parseInt(s().id)))}catch(k){h(g,"加载算法信息失败"),console.error("Failed to load algorithm:",k)}finally{h(E,!1)}}async function O(){if(a(i))try{switch(h(E,!0),h(g,""),a(i).name){case"冒泡排序":h(n,await la.generateBubbleSort(u));break;case"快速排序":h(n,await la.generateQuickSort(u));break;case"二分查找":const k=[...u].sort((y,M)=>y-M),R=u[0];h(n,await la.generateBinarySearch(k,R));break;case"归并排序":h(n,await j(u));break;case"深度优先搜索":case"广度优先搜索":h(n,await p(a(i).name));break;default:a(i).visualizationData&&h(n,JSON.parse(a(i).visualizationData))}h(l,0)}catch(k){h(g,"生成可视化数据失败"),console.error("Failed to generate visualization:",k)}finally{h(E,!1)}}function P(){a(v)?V():B()}function B(){!a(n)||a(l)>=a(n).steps.length-1||(h(v,!0),x=setInterval(()=>{a(l)<a(n).steps.length-1?ra(l):V()},a(o)))}function V(){h(v,!1),x&&(clearInterval(x),x=null)}function w(){V(),h(l,0)}function F(){V(),a(l)>0&&ra(l,-1)}function U(){V(),a(n)&&a(l)<a(n).steps.length-1&&ra(l)}function _(){try{const k=a(d).split(",").map(R=>parseInt(R.trim())).filter(R=>!isNaN(R));k.length>0&&k.length<=20?(u=k,O()):alert("请输入1-20个有效数字，用逗号分隔")}catch{alert("输入格式错误")}}function z(){u=Array.from({length:8},()=>Math.floor(Math.random()*90)+10),h(d,u.join(", ")),O()}async function j(k){const R=[],y=k.slice();let M=0;function W(D,H,ee,G=0){if(H>=ee)return;const ie=Math.floor((H+ee)/2);R.push({step:++M,action:"divide",array:D.slice(),left:H,right:ee,mid:ie,level:G}),W(D,H,ie,G+1),W(D,ie+1,ee,G+1),J(D,H,ie,ee,G)}function J(D,H,ee,G,ie){const pe=D.slice(H,ee+1),ue=D.slice(ee+1,G+1);let Z=0,ce=0,_e=H;for(;Z<pe.length&&ce<ue.length;)R.push({step:++M,action:"compare",array:D.slice(),comparing:[pe[Z],ue[ce]],positions:[H+Z,ee+1+ce],level:ie}),pe[Z]<=ue[ce]?(D[_e]=pe[Z],Z++):(D[_e]=ue[ce],ce++),R.push({step:++M,action:"merge",array:D.slice(),positions:[_e],level:ie}),_e++;for(;Z<pe.length;)D[_e]=pe[Z],R.push({step:++M,action:"merge",array:D.slice(),positions:[_e],level:ie}),Z++,_e++;for(;ce<ue.length;)D[_e]=ue[ce],R.push({step:++M,action:"merge",array:D.slice(),positions:[_e],level:ie}),ce++,_e++}return W(y,0,y.length-1),R.push({step:++M,action:"complete",array:y.slice()}),{algorithm:"merge_sort",input:k,output:y,steps:R,totalSteps:M}}async function p(k){const R=[{id:0,x:100,y:100,visited:!1},{id:1,x:200,y:50,visited:!1},{id:2,x:200,y:150,visited:!1},{id:3,x:300,y:100,visited:!1},{id:4,x:400,y:50,visited:!1},{id:5,x:400,y:150,visited:!1}],y=[{from:0,to:1},{from:0,to:2},{from:1,to:3},{from:2,to:3},{from:3,to:4},{from:3,to:5}],M=[];let W=0;if(k==="深度优先搜索"){const J=new Set,D=[0];for(M.push({step:++W,action:"start",nodes:R.map(H=>({...H})),edges:y,current:0,stack:[0]});D.length>0;){const H=D.pop();if(!J.has(H)){J.add(H),R[H].visited=!0,M.push({step:++W,action:"visit",nodes:R.map(G=>({...G})),edges:y,current:H,visited:Array.from(J)});const ee=y.filter(G=>G.from===H).map(G=>G.to);for(const G of ee.reverse())J.has(G)||D.push(G);M.push({step:++W,action:"add_neighbors",nodes:R.map(G=>({...G})),edges:y,current:H,stack:[...D]})}}}else{const J=new Set,D=[0];for(M.push({step:++W,action:"start",nodes:R.map(H=>({...H})),edges:y,current:0,queue:[0]});D.length>0;){const H=D.shift();if(!J.has(H)){J.add(H),R[H].visited=!0,M.push({step:++W,action:"visit",nodes:R.map(G=>({...G})),edges:y,current:H,visited:Array.from(J)});const ee=y.filter(G=>G.from===H).map(G=>G.to);for(const G of ee)J.has(G)||D.push(G);M.push({step:++W,action:"add_neighbors",nodes:R.map(G=>({...G})),edges:y,current:H,queue:[...D]})}}}return M.push({step:++W,action:"complete",nodes:R.map(J=>({...J})),edges:y}),{algorithm:k==="深度优先搜索"?"dfs":"bfs",input:{nodes:R,edges:y},output:{nodes:R,edges:y},steps:M,totalSteps:W}}function A(k){return k.category==="searching"?"searching":k.category==="graph"?"graph":"sorting"}function q(){return!a(n)||a(l)>=a(n).steps.length?null:a(n).steps[a(l)]}pt(()=>(m().then(()=>{a(i)&&(h(d,u.join(", ")),O())}),()=>{V()})),Tt(()=>{},()=>{h(r,q())}),xr(),lt();var I=el(),L=c(I);{var $=k=>{var R=Ji();b(k,R)},C=k=>{var R=ye(),y=ve(R);{var M=J=>{var D=Yi(),H=c(D),ee=c(H),G=f(H,2);Y(()=>T(ee,a(g))),K("click",G,m),b(J,D)},W=J=>{var D=ye(),H=ve(D);{var ee=G=>{var ie=Zi(),pe=ve(ie),ue=c(pe),Z=c(ue),ce=f(ue,2),_e=c(ce),le=f(ce,2),Ae=c(le),Ke=f(c(Ae)),we=c(Ke),te=f(Ae,2),Ee=f(c(te)),Re=c(Ee),Me=f(pe,2),Pe=c(Me),Le=c(Pe),be=f(c(Le)),Te=f(Le,2),je=f(Te,2),Ne=f(Me,2);{var xe=ze=>{var Be=Xi(),rt=c(Be);{let gt=Qt(()=>(a(i),S(()=>A(a(i)))));Hi(rt,{get steps(){return a(n),S(()=>a(n).steps)},get currentStep(){return a(l)},width:800,height:400,get algorithmType(){return a(gt)}})}var ot=f(rt,2);{var _t=gt=>{var lr=Ki(),or=c(lr),Jr=c(or),Yr=f(or,2),Kr=c(Yr);{var Xr=kt=>{var qe=mt();Y(at=>T(qe,`比较位置 ${at??""} 的元素`),[()=>(a(r),S(()=>a(r).positions?.join(" 和 ")))]),b(kt,qe)},Qr=kt=>{var qe=ye(),at=ve(qe);{var Et=Ge=>{var ct=mt();Y(Mt=>T(ct,`交换位置 ${Mt??""} 的元素`),[()=>(a(r),S(()=>a(r).positions?.join(" 和 ")))]),b(Ge,ct)},cr=Ge=>{var ct=ye(),Mt=ve(ct);{var Zr=Ft=>{var vr=mt();Y(()=>T(vr,`第 ${a(r),S(()=>a(r).sorted_position)??""} 个位置已排序完成`)),b(Ft,vr)},Ds=Ft=>{var vr=ye(),Ps=ve(vr);{var Ls=Ut=>{var Sr=mt("排序完成！");b(Ut,Sr)},js=Ut=>{var Sr=ye(),qs=ve(Sr);{var $s=Bt=>{var ur=mt();Y(()=>T(ur,`选择基准元素: ${a(r),S(()=>a(r).pivot_value)??""}`)),b(Bt,ur)},Fs=Bt=>{var ur=ye(),Us=ve(ur);{var Bs=Vt=>{var Cr=mt("分区完成，基准元素已就位");b(Vt,Cr)},Vs=Vt=>{var Cr=ye(),Gs=ve(Cr);{var Ws=Gt=>{var fr=mt();Y(()=>T(fr,`找到目标元素在位置 ${a(r),S(()=>a(r).found_index)??""}`)),b(Gt,fr)},Hs=Gt=>{var fr=ye(),Js=ve(fr);{var Ys=Wt=>{var Tr=mt("未找到目标元素");b(Wt,Tr)},Ks=Wt=>{var Tr=mt();Y(()=>T(Tr,(a(r),S(()=>a(r).action)))),b(Wt,Tr)};Q(Js,Wt=>{a(r),S(()=>a(r).action==="not_found")?Wt(Ys):Wt(Ks,!1)},!0)}b(Gt,fr)};Q(Gs,Gt=>{a(r),S(()=>a(r).action==="found")?Gt(Ws):Gt(Hs,!1)},!0)}b(Vt,Cr)};Q(Us,Vt=>{a(r),S(()=>a(r).action==="partition_complete")?Vt(Bs):Vt(Vs,!1)},!0)}b(Bt,ur)};Q(qs,Bt=>{a(r),S(()=>a(r).action==="select_pivot")?Bt($s):Bt(Fs,!1)},!0)}b(Ut,Sr)};Q(Ps,Ut=>{a(r),S(()=>a(r).action==="complete")?Ut(Ls):Ut(js,!1)},!0)}b(Ft,vr)};Q(Mt,Ft=>{a(r),S(()=>a(r).action==="round_complete")?Ft(Zr):Ft(Ds,!1)},!0)}b(Ge,ct)};Q(at,Ge=>{a(r),S(()=>a(r).action==="swap")?Ge(Et):Ge(cr,!1)},!0)}b(kt,qe)};Q(Kr,kt=>{a(r),S(()=>a(r).action==="compare")?kt(Xr):kt(Qr,!1)})}Y(()=>T(Jr,`步骤 ${a(l)+1} / ${a(n),S(()=>a(n).steps.length)??""}`)),b(gt,lr)};Q(ot,gt=>{a(r)&&gt(_t)})}var Rt=f(ot,2),me=c(Rt),ke=f(me,2),ae=f(ke,2),fe=c(ae),he=f(ae,2),Ve=f(he,2),ne=c(Ve),ht=f(c(ne)),kr=f(ht,2),Er=c(kr);Y(()=>{Ms(ae,"title",a(v)?"暂停":"播放"),T(fe,a(v)?"⏸":"▶"),T(Er,`${(2100-a(o))/100}x`)}),K("click",me,w),K("click",ke,F),K("click",ae,P),K("click",he,U),yr(ht,()=>a(o),gt=>h(o,gt)),b(ze,Be)},Ue=ze=>{var Be=Qi();b(ze,Be)};Q(Ne,ze=>{a(n)?ze(xe):ze(Ue,!1)})}Y(()=>{T(Z,(a(i),S(()=>a(i).name))),T(_e,(a(i),S(()=>a(i).description))),T(we,(a(i),S(()=>a(i).timeComplexity))),T(Re,(a(i),S(()=>a(i).spaceComplexity)))}),yr(be,()=>a(d),ze=>h(d,ze)),K("click",Te,_),K("click",je,z),b(G,ie)};Q(H,G=>{a(i)&&G(ee)},!0)}b(J,D)};Q(y,J=>{a(g)?J(M):J(W,!1)},!0)}b(k,R)};Q(L,k=>{a(E)?k($):k(C,!1)})}b(e,I),tt()}var rl=N('<option class="svelte-c0t6h5"> </option>'),al=N('<div class="loading svelte-c0t6h5"><div class="spinner svelte-c0t6h5"></div> <p class="svelte-c0t6h5">加载中...</p></div>'),sl=N('<div class="error svelte-c0t6h5"><p class="svelte-c0t6h5"> </p> <button class="retry-btn svelte-c0t6h5">重试</button></div>'),nl=N('<div class="empty svelte-c0t6h5"><p class="svelte-c0t6h5">没有找到匹配的题目</p></div>'),il=N('<div class="table-row svelte-c0t6h5"><div class="col-title svelte-c0t6h5"><h3 class="svelte-c0t6h5"> </h3> <p class="description svelte-c0t6h5"> </p></div> <div class="col-difficulty svelte-c0t6h5"><span class="difficulty-badge svelte-c0t6h5"> </span></div> <div class="col-category svelte-c0t6h5"><span class="category-tag svelte-c0t6h5"> </span></div> <div class="col-limits svelte-c0t6h5"><div class="limit-item svelte-c0t6h5"><span class="limit-label svelte-c0t6h5">时间:</span> <span class="limit-value svelte-c0t6h5"> </span></div> <div class="limit-item svelte-c0t6h5"><span class="limit-label svelte-c0t6h5">内存:</span> <span class="limit-value svelte-c0t6h5"> </span></div></div> <div class="col-action svelte-c0t6h5"><button class="solve-btn svelte-c0t6h5">开始解题</button></div></div>'),ll=N('<div class="problem-table svelte-c0t6h5"><div class="table-header svelte-c0t6h5"><div class="col-title svelte-c0t6h5">题目</div> <div class="col-difficulty svelte-c0t6h5">难度</div> <div class="col-category svelte-c0t6h5">分类</div> <div class="col-limits svelte-c0t6h5">限制</div> <div class="col-action svelte-c0t6h5">操作</div></div> <!></div>'),ol=N('<div class="problem-list svelte-c0t6h5"><div class="header svelte-c0t6h5"><h1 class="svelte-c0t6h5">算法题目</h1> <p class="svelte-c0t6h5">挑战各种算法问题，提升编程技能</p></div> <div class="filters svelte-c0t6h5"><div class="search-box svelte-c0t6h5"><input type="text" placeholder="搜索题目..." class="search-input svelte-c0t6h5"/></div> <div class="filter-controls svelte-c0t6h5"><select class="filter-select svelte-c0t6h5"><option class="svelte-c0t6h5">所有难度</option><option class="svelte-c0t6h5">简单</option><option class="svelte-c0t6h5">中等</option><option class="svelte-c0t6h5">困难</option></select> <select class="filter-select svelte-c0t6h5"><option class="svelte-c0t6h5">所有分类</option><!></select></div></div> <!></div>');function cl(e,t){et(t,!1);const r=X();let s=[],i=X([]),n=X(""),l=X(""),v=X(""),o=X(!0),u=X("");async function d(){try{h(o,!0),h(u,""),s=await Os.getAll(),E()}catch(y){h(u,"加载题目列表失败"),console.error("Failed to load problems:",y)}finally{h(o,!1)}}function E(){h(i,s.filter(y=>{const M=!a(n)||y.difficulty===a(n),W=!a(l)||y.category===a(l),J=!a(v)||y.title.toLowerCase().includes(a(v).toLowerCase())||y.description&&y.description.toLowerCase().includes(a(v).toLowerCase());return M&&W&&J}))}function g(y){He.navigate(`/problems/${y.id}`)}function x(y){return{easy:"#10b981",medium:"#f59e0b",hard:"#ef4444"}[y]||"#6b7280"}function m(y){return{easy:"简单",medium:"中等",hard:"困难"}[y]||y}function O(){const y=new Set(s.map(M=>M.category).filter(Boolean));return Array.from(y)}function P(){E()}function B(){E()}pt(()=>{d()}),Tt(()=>{},()=>{h(r,O())}),xr(),lt();var V=ol(),w=f(c(V),2),F=c(w),U=c(F),_=f(F,2),z=c(_);Y(()=>{a(n),ar(()=>{})});var j=c(z);j.value=j.__value="";var p=f(j);p.value=p.__value="easy";var A=f(p);A.value=A.__value="medium";var q=f(A);q.value=q.__value="hard";var I=f(z,2);Y(()=>{a(l),ar(()=>{a(r)})});var L=c(I);L.value=L.__value="";var $=f(L);Fe($,1,()=>a(r),$e,(y,M)=>{var W=rl(),J=c(W),D={};Y(()=>{T(J,a(M)),D!==(D=a(M))&&(W.value=(W.__value=a(M))??"")}),b(y,W)});var C=f(w,2);{var k=y=>{var M=al();b(y,M)},R=y=>{var M=ye(),W=ve(M);{var J=H=>{var ee=sl(),G=c(ee),ie=c(G),pe=f(G,2);Y(()=>T(ie,a(u))),K("click",pe,d),b(H,ee)},D=H=>{var ee=ye(),G=ve(ee);{var ie=ue=>{var Z=nl();b(ue,Z)},pe=ue=>{var Z=ll(),ce=f(c(Z),2);Fe(ce,1,()=>a(i),$e,(_e,le)=>{var Ae=il(),Ke=c(Ae),we=c(Ke),te=c(we),Ee=f(we,2),Re=c(Ee),Me=f(Ke,2),Pe=c(Me),Le=c(Pe),be=f(Me,2),Te=c(be),je=c(Te),Ne=f(be,2),xe=c(Ne),Ue=f(c(xe),2),ze=c(Ue),Be=f(xe,2),rt=f(c(Be),2),ot=c(rt),_t=f(Ne,2),Rt=c(_t);Y((me,ke)=>{T(te,(a(le),S(()=>a(le).title))),T(Re,(a(le),S(()=>a(le).description||"暂无描述"))),Je(Pe,`background-color: ${me??""}`),T(Le,ke),T(je,(a(le),S(()=>a(le).category||"未分类"))),T(ze,`${a(le),S(()=>a(le).timeLimit)??""}ms`),T(ot,`${a(le),S(()=>a(le).memoryLimit)??""}MB`)},[()=>(a(le),S(()=>x(a(le).difficulty))),()=>(a(le),S(()=>m(a(le).difficulty)))]),K("click",Rt,bi(()=>g(a(le)))),K("click",Ae,()=>g(a(le))),b(_e,Ae)}),b(ue,Z)};Q(G,ue=>{a(i),S(()=>a(i).length===0)?ue(ie):ue(pe,!1)},!0)}b(H,ee)};Q(W,H=>{a(u)?H(J):H(D,!1)},!0)}b(y,M)};Q(C,y=>{a(o)?y(k):y(R,!1)})}yr(U,()=>a(v),y=>h(v,y)),K("input",U,P),sr(z,()=>a(n),y=>h(n,y)),K("change",z,B),sr(I,()=>a(l),y=>h(l,y)),K("change",I,B),b(e,V),tt()}var vl=N('<div class="code-editor svelte-1hgkv1b"><div class="editor-container svelte-1hgkv1b"></div></div>');function ul(e,t){et(t,!1);let r=Oe(t,"code",12,""),s=Oe(t,"language",8,"java"),i=Oe(t,"height",8,"300px"),n=Oe(t,"theme",8,"vs-dark"),l=Oe(t,"readOnly",8,!1),v=X(),o=X(null);function u(){a(v)&&(Ar.defineTheme("custom-dark",{base:"vs-dark",inherit:!0,rules:[{token:"comment",foreground:"6A9955"},{token:"keyword",foreground:"569CD6"},{token:"string",foreground:"CE9178"},{token:"number",foreground:"B5CEA8"}],colors:{"editor.background":"#1e1e1e","editor.foreground":"#d4d4d4","editorLineNumber.foreground":"#858585","editor.selectionBackground":"#264f78","editor.inactiveSelectionBackground":"#3a3d41"}}),h(o,Ar.create(a(v),{value:r(),language:d(s()),theme:n()==="vs-dark"?"custom-dark":n(),readOnly:l(),automaticLayout:!0,fontSize:14,lineNumbers:"on",minimap:{enabled:!1},scrollBeyondLastLine:!1,wordWrap:"on",tabSize:4,insertSpaces:!0,folding:!0,lineDecorationsWidth:10,lineNumbersMinChars:3,renderLineHighlight:"line",selectOnLineNumbers:!0,roundedSelection:!1,cursorStyle:"line",cursorBlinking:"blink",contextmenu:!0,mouseWheelZoom:!0,quickSuggestions:{other:!0,comments:!1,strings:!1},suggestOnTriggerCharacters:!0,acceptSuggestionOnEnter:"on",acceptSuggestionOnCommitCharacter:!0,snippetSuggestions:"top",emptySelectionClipboard:!1,copyWithSyntaxHighlighting:!0,multiCursorModifier:"ctrlCmd",accessibilitySupport:"auto"})),a(o).onDidChangeModelContent(()=>{a(o)&&r(a(o).getValue())}),a(o).addCommand(ta.CtrlCmd|Ia.KeyS,()=>{console.log("Save shortcut pressed")}),a(o).addCommand(ta.Shift|ta.Alt|Ia.KeyF,()=>{a(o)?.getAction("editor.action.formatDocument")?.run()}))}function d(U){return{java:"java",python:"python",cpp:"cpp","c++":"cpp",javascript:"javascript",typescript:"typescript",html:"html",css:"css",json:"json",xml:"xml",sql:"sql"}[U.toLowerCase()]||"plaintext"}function E(){if(a(o)){const U=a(o).getModel();U&&Ar.setModelLanguage(U,d(s()))}}function g(){a(o)&&a(o).getValue()!==r()&&a(o).setValue(r())}function x(){a(o)&&Ar.setTheme(n()==="vs-dark"?"custom-dark":n())}function m(){a(o)&&a(o).getAction("editor.action.formatDocument")?.run()}function O(){return a(o)}function P(){a(o)&&a(o).focus()}function B(U){if(a(o)){const _=a(o).getSelection();_&&a(o).executeEdits("",[{range:_,text:U,forceMoveMarkers:!0}])}}function V(){if(a(o)){const U=a(o).getSelection();if(U)return a(o).getModel()?.getValueInRange(U)||""}return""}pt(()=>{setTimeout(()=>{u()},100)}),Is(()=>{a(o)&&(a(o).dispose(),h(o,null))}),Tt(()=>a(o),()=>{a(o)&&E()}),Tt(()=>a(o),()=>{a(o)&&g()}),Tt(()=>a(o),()=>{a(o)&&x()}),xr(),lt();var w=vl(),F=c(w);return Ns(F,U=>h(v,U),()=>a(v)),Y(()=>Je(w,`height: ${i()??""};`)),b(e,w),pr(t,"formatCode",m),pr(t,"getEditor",O),pr(t,"focus",P),pr(t,"insertText",B),pr(t,"getSelectedText",V),tt({formatCode:m,getEditor:O,focus:P,insertText:B,getSelectedText:V})}var fl=N('<div class="error-message svelte-7jeref"><span class="error-icon svelte-7jeref">⚠</span> </div>'),dl=N('<div class="empty-message svelte-7jeref"><span class="empty-icon svelte-7jeref">📝</span> 暂无测试用例</div>'),pl=N('<button><span class="tab-icon svelte-7jeref"> </span> <span class="tab-text"> </span></button>'),_l=N('<span class="execution-time svelte-7jeref"> </span>'),hl=N('<div class="result-summary svelte-7jeref"><span class="result-status svelte-7jeref"> </span> <!></div>'),gl=N('<div class="actual-output-section svelte-7jeref"><div class="section-header svelte-7jeref"><h4 class="svelte-7jeref">实际输出</h4> <button class="copy-btn svelte-7jeref" title="复制实际输出">📋</button></div> <div><pre class="svelte-7jeref"> </pre></div></div>'),ml=N('<div class="error-section svelte-7jeref"><div class="section-header svelte-7jeref"><h4 class="svelte-7jeref">错误信息</h4></div> <div class="error-block svelte-7jeref"><pre class="svelte-7jeref"> </pre></div></div>'),bl=N("<!> <!>",1),yl=N('<div class="test-case-content svelte-7jeref"><div class="case-header svelte-7jeref"><h3 class="svelte-7jeref"> </h3> <!></div> <div class="case-details svelte-7jeref"><div class="input-section svelte-7jeref"><div class="section-header svelte-7jeref"><h4 class="svelte-7jeref">输入</h4> <button class="copy-btn svelte-7jeref" title="复制输入">📋</button></div> <div class="code-block svelte-7jeref"><pre class="svelte-7jeref"> </pre></div></div> <div class="output-section svelte-7jeref"><div class="section-header svelte-7jeref"><h4 class="svelte-7jeref">期望输出</h4> <button class="copy-btn svelte-7jeref" title="复制期望输出">📋</button></div> <div class="code-block svelte-7jeref"><pre class="svelte-7jeref"> </pre></div></div> <!></div></div>'),wl=N('<div class="test-case-tabs svelte-7jeref"></div> <!>',1),xl=N('<div class="test-case-runner svelte-7jeref"><!></div>');function zl(e,t){et(t,!1);let r=Oe(t,"testCases",8,"[]"),s=Oe(t,"showResults",8,!1),i=Oe(t,"results",24,()=>[]),n=X([]),l=X(0),v=X("");function o(){try{h(v,"");const w=JSON.parse(r());Array.isArray(w)?h(n,w.map((F,U)=>({id:U+1,input:F.input||"",output:F.output||F.expected||"",description:F.description||`测试用例 ${U+1}`}))):(h(n,[]),h(v,"测试用例格式错误：应为数组格式"))}catch(w){h(n,[]),h(v,"测试用例解析失败：JSON格式错误"),console.error("Failed to parse test cases:",w)}}function u(w){try{const F=JSON.parse(w);return JSON.stringify(F,null,2)}catch{return w}}function d(w){return!s()||!i()||w>=i().length?"pending":i()[w]?.passed?"passed":"failed"}function E(w){return!s()||!i()||w>=i().length?null:i()[w]}function g(w){return{passed:"#10b981",failed:"#ef4444",pending:"#6b7280"}[w]||"#6b7280"}function x(w){return{passed:"✓",failed:"✗",pending:"○"}[w]||"○"}async function m(w){try{await navigator.clipboard.writeText(w)}catch(F){console.error("Failed to copy to clipboard:",F)}}pt(()=>{o()}),Tt(()=>{},()=>{o()}),xr(),lt();var O=xl(),P=c(O);{var B=w=>{var F=fl(),U=f(c(F));Y(()=>T(U,` ${a(v)??""}`)),b(w,F)},V=w=>{var F=ye(),U=ve(F);{var _=j=>{var p=dl();b(j,p)},z=j=>{var p=wl(),A=ve(p);Fe(A,5,()=>a(n),$e,(L,$,C)=>{var k=pl();let R;var y=c(k),M=c(y),W=f(y,2),J=c(W);Y((D,H,ee)=>{R=st(k,1,"tab-button svelte-7jeref",null,R,D),Je(y,`color: ${H??""}`),T(M,ee),T(J,`用例 ${a($),S(()=>a($).id)??""}`)},[()=>({active:a(l)===C}),()=>S(()=>g(d(C))),()=>S(()=>x(d(C)))]),K("click",k,()=>h(l,C)),b(L,k)});var q=f(A,2);{var I=L=>{var $=yl(),C=c($),k=c(C),R=c(k),y=f(k,2);{var M=we=>{const te=Qt(()=>(a(l),S(()=>E(a(l)))));var Ee=ye(),Re=ve(Ee);{var Me=Pe=>{var Le=hl(),be=c(Le),Te=c(be),je=f(be,2);{var Ne=xe=>{var Ue=_l(),ze=c(Ue);Y(()=>T(ze,`执行时间: ${nt(a(te)),S(()=>a(te).executionTime)??""}ms`)),b(xe,Ue)};Q(je,xe=>{nt(a(te)),S(()=>a(te).executionTime)&&xe(Ne)})}Y(xe=>{Je(be,`color: ${xe??""}`),T(Te,(nt(a(te)),S(()=>a(te).passed?"通过":"失败")))},[()=>(a(l),S(()=>g(d(a(l)))))]),b(Pe,Le)};Q(Re,Pe=>{a(te)&&Pe(Me)})}b(we,Ee)};Q(y,we=>{s()&&we(M)})}var W=f(C,2),J=c(W),D=c(J),H=f(c(D),2),ee=f(D,2),G=c(ee),ie=c(G),pe=f(J,2),ue=c(pe),Z=f(c(ue),2),ce=f(ue,2),_e=c(ce),le=c(_e),Ae=f(pe,2);{var Ke=we=>{const te=Qt(()=>(a(l),S(()=>E(a(l)))));var Ee=bl(),Re=ve(Ee);{var Me=be=>{var Te=gl(),je=c(Te),Ne=f(c(je),2),xe=f(je,2);let Ue;var ze=c(xe),Be=c(ze);Y((rt,ot)=>{Ue=st(xe,1,"code-block svelte-7jeref",null,Ue,rt),T(Be,ot)},[()=>({error:!a(te).passed}),()=>(nt(a(te)),S(()=>u(a(te).actualOutput)))]),K("click",Ne,()=>m(a(te).actualOutput)),b(be,Te)};Q(Re,be=>{nt(a(te)),S(()=>a(te)&&a(te).actualOutput!==void 0)&&be(Me)})}var Pe=f(Re,2);{var Le=be=>{var Te=ml(),je=f(c(Te),2),Ne=c(je),xe=c(Ne);Y(()=>T(xe,(nt(a(te)),S(()=>a(te).error)))),b(be,Te)};Q(Pe,be=>{nt(a(te)),S(()=>a(te)&&a(te).error)&&be(Le)})}b(we,Ee)};Q(Ae,we=>{s()&&we(Ke)})}Y((we,te)=>{T(R,(a(n),a(l),S(()=>a(n)[a(l)].description))),T(ie,we),T(le,te)},[()=>(a(n),a(l),S(()=>u(a(n)[a(l)].input))),()=>(a(n),a(l),S(()=>u(a(n)[a(l)].output)))]),K("click",H,()=>m(a(n)[a(l)].input)),K("click",Z,()=>m(a(n)[a(l)].output)),b(L,$)};Q(q,L=>{a(n),a(l),S(()=>a(n)[a(l)])&&L(I)})}b(j,p)};Q(U,j=>{a(n),S(()=>a(n).length===0)?j(_):j(z,!1)},!0)}b(w,F)};Q(P,w=>{a(v)?w(B):w(V,!1)})}b(e,O),tt()}var kl=N('<div class="loading svelte-u7zubw"><div class="spinner svelte-u7zubw"></div> <p class="svelte-u7zubw">加载中...</p></div>'),El=N('<div class="error svelte-u7zubw"><p class="svelte-u7zubw"> </p> <button class="retry-btn svelte-u7zubw">重试</button></div>'),Sl=N('<div class="examples-section svelte-u7zubw"><h2 class="svelte-u7zubw">示例</h2> <!></div>'),Cl=N('<div class="complexity-section svelte-u7zubw"><h2 class="svelte-u7zubw">期望复杂度</h2> <p class="svelte-u7zubw"> </p></div>'),Tl=N('<option class="svelte-u7zubw"> </option>'),Al=N('<div class="submission-metrics svelte-u7zubw"><span class="svelte-u7zubw"> </span> <span class="svelte-u7zubw"> </span> <span class="svelte-u7zubw"> </span></div>'),Rl=N('<div class="error-message svelte-u7zubw"> </div>'),Ml=N('<div class="submission-item svelte-u7zubw"><div class="submission-status svelte-u7zubw"><span class="status-badge svelte-u7zubw"> </span></div> <div class="submission-info svelte-u7zubw"><span class="language svelte-u7zubw"> </span> <span class="time svelte-u7zubw"> </span></div> <!></div>'),Nl=N('<div class="submissions-section svelte-u7zubw"><h2 class="svelte-u7zubw">提交历史</h2> <div class="submissions-list svelte-u7zubw"></div></div>'),Il=N('<div class="problem-header svelte-u7zubw"><div class="title-section svelte-u7zubw"><h1 class="svelte-u7zubw"> </h1> <div class="meta-info svelte-u7zubw"><span class="difficulty-badge svelte-u7zubw"> </span> <span class="category-tag svelte-u7zubw"> </span></div></div> <div class="limits-info svelte-u7zubw"><div class="limit-item svelte-u7zubw"><span class="label svelte-u7zubw">时间限制:</span> <span class="value svelte-u7zubw"> </span></div> <div class="limit-item svelte-u7zubw"><span class="label svelte-u7zubw">内存限制:</span> <span class="value svelte-u7zubw"> </span></div></div></div> <div class="content-layout svelte-u7zubw"><div class="problem-content svelte-u7zubw"><div class="description-section svelte-u7zubw"><h2 class="svelte-u7zubw">题目描述</h2> <div class="description svelte-u7zubw"> </div></div> <!> <!></div> <div class="code-section svelte-u7zubw"><div class="editor-header svelte-u7zubw"><h2 class="svelte-u7zubw">代码编辑器</h2> <div class="language-selector svelte-u7zubw"><select class="svelte-u7zubw"></select></div></div> <div class="editor-container svelte-u7zubw"><!></div> <div class="submit-section svelte-u7zubw"><button class="submit-btn svelte-u7zubw"> </button></div></div></div> <!>',1),Ol=N('<div class="problem-detail svelte-u7zubw"><!></div>');function Dl(e,t){et(t,!1);let r=Oe(t,"params",24,()=>({})),s=X(null),i=X([]),n=X(""),l=X("java"),v=X(!0),o=X(!1),u=X(""),d="user123";const E=[{id:"java",name:"Java",extension:"java"},{id:"python",name:"Python",extension:"py"},{id:"cpp",name:"C++",extension:"cpp"}];async function g(){if(r().id)try{h(v,!0),h(u,""),h(s,await Os.getById(parseInt(r().id))),a(s).solutionTemplate?h(n,a(s).solutionTemplate):h(n,O(a(l))),await x()}catch(p){h(u,"加载题目信息失败"),console.error("Failed to load problem:",p)}finally{h(v,!1)}}async function x(){if(a(s))try{h(i,await _a.getAll(d,a(s).id))}catch(p){console.error("Failed to load submissions:",p)}}async function m(){if(!a(s)||!a(n).trim()){alert("请输入代码");return}try{h(o,!0);const p={problemId:a(s).id,userId:d,language:a(l),code:a(n)},A=await _a.submit(p);await x(),alert("代码提交成功！")}catch(p){alert("提交失败，请重试"),console.error("Failed to submit code:",p)}finally{h(o,!1)}}function O(p){const A={java:`public class Solution {
    public int[] solve(int[] nums) {
        // 在这里实现你的解决方案
        return nums;
    }
}`,python:`def solve(nums):
    # 在这里实现你的解决方案
    return nums`,cpp:`#include <vector>
using namespace std;

class Solution {
public:
    vector<int> solve(vector<int>& nums) {
        // 在这里实现你的解决方案
        return nums;
    }
};`};return A[p]||A.java}function P(){a(s)?.solutionTemplate?h(n,a(s).solutionTemplate):h(n,O(a(l)))}function B(p){return{ACCEPTED:"#10b981",WRONG_ANSWER:"#ef4444",TIME_LIMIT_EXCEEDED:"#f59e0b",MEMORY_LIMIT_EXCEEDED:"#f59e0b",COMPILATION_ERROR:"#ef4444",RUNTIME_ERROR:"#ef4444",PENDING:"#6b7280",RUNNING:"#3b82f6"}[p]||"#6b7280"}function V(p){return{ACCEPTED:"通过",WRONG_ANSWER:"答案错误",TIME_LIMIT_EXCEEDED:"超时",MEMORY_LIMIT_EXCEEDED:"内存超限",COMPILATION_ERROR:"编译错误",RUNTIME_ERROR:"运行错误",PENDING:"等待中",RUNNING:"运行中"}[p]||p}function w(p){return{easy:"#10b981",medium:"#f59e0b",hard:"#ef4444"}[p]||"#6b7280"}function F(p){return{easy:"简单",medium:"中等",hard:"困难"}[p]||p}pt(()=>{g()}),lt();var U=Ol(),_=c(U);{var z=p=>{var A=kl();b(p,A)},j=p=>{var A=ye(),q=ve(A);{var I=$=>{var C=El(),k=c(C),R=c(k),y=f(k,2);Y(()=>T(R,a(u))),K("click",y,g),b($,C)},L=$=>{var C=ye(),k=ve(C);{var R=y=>{var M=Il(),W=ve(M),J=c(W),D=c(J),H=c(D),ee=f(D,2),G=c(ee),ie=c(G),pe=f(G,2),ue=c(pe),Z=f(J,2),ce=c(Z),_e=f(c(ce),2),le=c(_e),Ae=f(ce,2),Ke=f(c(Ae),2),we=c(Ke),te=f(W,2),Ee=c(te),Re=c(Ee),Me=f(c(Re),2),Pe=c(Me),Le=f(Re,2);{var be=ae=>{var fe=Sl(),he=f(c(fe),2);zl(he,{get testCases(){return a(s),S(()=>a(s).testCases)}}),b(ae,fe)};Q(Le,ae=>{a(s),S(()=>a(s).testCases)&&ae(be)})}var Te=f(Le,2);{var je=ae=>{var fe=Cl(),he=f(c(fe),2),Ve=c(he);Y(()=>T(Ve,(a(s),S(()=>a(s).expectedComplexity)))),b(ae,fe)};Q(Te,ae=>{a(s),S(()=>a(s).expectedComplexity)&&ae(je)})}var Ne=f(Ee,2),xe=c(Ne),Ue=f(c(xe),2),ze=c(Ue);Y(()=>{a(l),ar(()=>{})}),Fe(ze,5,()=>E,$e,(ae,fe)=>{var he=Tl(),Ve=c(he),ne={};Y(()=>{T(Ve,(a(fe),S(()=>a(fe).name))),ne!==(ne=(a(fe),S(()=>a(fe).id)))&&(he.value=(he.__value=(a(fe),S(()=>a(fe).id)))??"")}),b(ae,he)});var Be=f(xe,2),rt=c(Be);ul(rt,{get language(){return a(l)},height:"400px",get code(){return a(n)},set code(ae){h(n,ae)},$$legacy:!0});var ot=f(Be,2),_t=c(ot),Rt=c(_t),me=f(te,2);{var ke=ae=>{var fe=Nl(),he=f(c(fe),2);Fe(he,5,()=>a(i),$e,(Ve,ne)=>{var ht=Ml(),kr=c(ht),Er=c(kr),gt=c(Er),lr=f(kr,2),or=c(lr),Jr=c(or),Yr=f(or,2),Kr=c(Yr),Xr=f(lr,2);{var Qr=qe=>{var at=Al(),Et=c(at),cr=c(Et),Ge=f(Et,2),ct=c(Ge),Mt=f(Ge,2),Zr=c(Mt);Y(()=>{T(cr,`执行时间: ${a(ne),S(()=>a(ne).executionTime)??""}ms`),T(ct,`内存使用: ${a(ne),S(()=>a(ne).memoryUsage)??""}KB`),T(Zr,`通过: ${a(ne),S(()=>a(ne).passedTests)??""}/${a(ne),S(()=>a(ne).totalTests)??""}`)}),b(qe,at)},kt=qe=>{var at=ye(),Et=ve(at);{var cr=Ge=>{var ct=Rl(),Mt=c(ct);Y(()=>T(Mt,(a(ne),S(()=>a(ne).errorMessage)))),b(Ge,ct)};Q(Et,Ge=>{a(ne),S(()=>a(ne).errorMessage)&&Ge(cr)},!0)}b(qe,at)};Q(Xr,qe=>{a(ne),S(()=>a(ne).status==="ACCEPTED")?qe(Qr):qe(kt,!1)})}Y((qe,at,Et)=>{Je(Er,`background-color: ${qe??""}`),T(gt,at),T(Jr,(a(ne),S(()=>a(ne).language))),T(Kr,Et)},[()=>(a(ne),S(()=>B(a(ne).status))),()=>(a(ne),S(()=>V(a(ne).status))),()=>(a(ne),S(()=>new Date(a(ne).createdAt).toLocaleString()))]),b(Ve,ht)}),b(ae,fe)};Q(me,ae=>{a(i),S(()=>a(i).length>0)&&ae(ke)})}Y((ae,fe,he)=>{T(H,(a(s),S(()=>a(s).title))),Je(G,`background-color: ${ae??""}`),T(ie,fe),T(ue,(a(s),S(()=>a(s).category||"未分类"))),T(le,`${a(s),S(()=>a(s).timeLimit)??""}ms`),T(we,`${a(s),S(()=>a(s).memoryLimit)??""}MB`),T(Pe,(a(s),S(()=>a(s).description||"暂无描述"))),_t.disabled=he,T(Rt,a(o)?"提交中...":"提交代码")},[()=>(a(s),S(()=>w(a(s).difficulty))),()=>(a(s),S(()=>F(a(s).difficulty))),()=>(a(o),a(n),S(()=>a(o)||!a(n).trim()))]),sr(ze,()=>a(l),ae=>h(l,ae)),K("change",ze,P),K("click",_t,m),b(y,M)};Q(k,y=>{a(s)&&y(R)},!0)}b($,C)};Q(q,$=>{a(u)?$(I):$(L,!1)},!0)}b(p,A)};Q(_,p=>{a(v)?p(z):p(j,!1)})}b(e,U),tt()}var Pl=N('<option class="svelte-kfo99z"> </option>'),Ll=N('<option class="svelte-kfo99z"> </option>'),jl=N('<div class="loading svelte-kfo99z"><div class="spinner svelte-kfo99z"></div> <p class="svelte-kfo99z">加载中...</p></div>'),ql=N('<div class="error svelte-kfo99z"><p class="svelte-kfo99z"> </p> <button class="retry-btn svelte-kfo99z">重试</button></div>'),$l=N('<div class="empty svelte-kfo99z"><p class="svelte-kfo99z">没有找到匹配的提交记录</p></div>'),Fl=N('<div class="test-results svelte-kfo99z"> </div>'),Ul=N('<div class="error-message svelte-kfo99z"> </div>'),Bl=N('<div class="metric-item svelte-kfo99z"><span class="metric-label svelte-kfo99z">时间:</span> <span class="metric-value svelte-kfo99z"> </span></div> <div class="metric-item svelte-kfo99z"><span class="metric-label svelte-kfo99z">内存:</span> <span class="metric-value svelte-kfo99z"> </span></div>',1),Vl=N('<span class="no-metrics svelte-kfo99z">-</span>'),Gl=N('<div class="table-row svelte-kfo99z"><div class="col-problem svelte-kfo99z"><h3 class="svelte-kfo99z"> </h3> <p class="problem-id svelte-kfo99z"> </p></div> <div class="col-status svelte-kfo99z"><span class="status-badge svelte-kfo99z"> </span> <!></div> <div class="col-language svelte-kfo99z"><span class="language-tag svelte-kfo99z"> </span></div> <div class="col-metrics svelte-kfo99z"><!></div> <div class="col-time svelte-kfo99z"><span class="time-relative svelte-kfo99z"> </span> <span class="time-absolute svelte-kfo99z"> </span></div></div>'),Wl=N('<div class="submission-table svelte-kfo99z"><div class="table-header svelte-kfo99z"><div class="col-problem svelte-kfo99z">题目</div> <div class="col-status svelte-kfo99z">状态</div> <div class="col-language svelte-kfo99z">语言</div> <div class="col-metrics svelte-kfo99z">性能</div> <div class="col-time svelte-kfo99z">提交时间</div></div> <!></div>'),Hl=N('<div class="submission-list svelte-kfo99z"><div class="header svelte-kfo99z"><h1 class="svelte-kfo99z">提交记录</h1> <p class="svelte-kfo99z">查看你的代码提交历史和执行结果</p></div> <div class="filters svelte-kfo99z"><div class="search-box svelte-kfo99z"><input type="text" placeholder="搜索题目..." class="search-input svelte-kfo99z"/></div> <div class="filter-controls svelte-kfo99z"><select class="filter-select svelte-kfo99z"></select> <select class="filter-select svelte-kfo99z"></select></div></div> <!></div>');function Jl(e,t){et(t,!1);let r=[],s=X([]),i=X(""),n=X(""),l=X(""),v=X(!0),o=X(""),u="user123";const d=[{value:"",label:"所有状态"},{value:"ACCEPTED",label:"通过"},{value:"WRONG_ANSWER",label:"答案错误"},{value:"TIME_LIMIT_EXCEEDED",label:"超时"},{value:"MEMORY_LIMIT_EXCEEDED",label:"内存超限"},{value:"COMPILATION_ERROR",label:"编译错误"},{value:"RUNTIME_ERROR",label:"运行错误"}],E=[{value:"",label:"所有语言"},{value:"java",label:"Java"},{value:"python",label:"Python"},{value:"cpp",label:"C++"}];async function g(){try{h(v,!0),h(o,""),r=await _a.getAll(u),x()}catch(C){h(o,"加载提交记录失败"),console.error("Failed to load submissions:",C)}finally{h(v,!1)}}function x(){h(s,r.filter(C=>{const k=!a(i)||C.status===a(i),R=!a(n)||C.language===a(n),y=!a(l)||C.problem&&C.problem.title.toLowerCase().includes(a(l).toLowerCase());return k&&R&&y}))}function m(C){return{ACCEPTED:"#10b981",WRONG_ANSWER:"#ef4444",TIME_LIMIT_EXCEEDED:"#f59e0b",MEMORY_LIMIT_EXCEEDED:"#f59e0b",COMPILATION_ERROR:"#ef4444",RUNTIME_ERROR:"#ef4444",PENDING:"#6b7280",RUNNING:"#3b82f6"}[C]||"#6b7280"}function O(C){return{ACCEPTED:"通过",WRONG_ANSWER:"答案错误",TIME_LIMIT_EXCEEDED:"超时",MEMORY_LIMIT_EXCEEDED:"内存超限",COMPILATION_ERROR:"编译错误",RUNTIME_ERROR:"运行错误",PENDING:"等待中",RUNNING:"运行中"}[C]||C}function P(C){return{java:"Java",python:"Python",cpp:"C++",javascript:"JavaScript"}[C]||C}function B(C){const k=new Date(C),y=new Date().getTime()-k.getTime(),M=Math.floor(y/(1e3*60)),W=Math.floor(y/(1e3*60*60)),J=Math.floor(y/(1e3*60*60*24));return M<1?"刚刚":M<60?`${M}分钟前`:W<24?`${W}小时前`:J<7?`${J}天前`:k.toLocaleDateString("zh-CN")}function V(){x()}function w(){x()}function F(C){console.log("View submission detail:",C)}pt(()=>{g()}),lt();var U=Hl(),_=f(c(U),2),z=c(_),j=c(z),p=f(z,2),A=c(p);Y(()=>{a(i),ar(()=>{})}),Fe(A,5,()=>d,$e,(C,k)=>{var R=Pl(),y=c(R),M={};Y(()=>{T(y,a(k).label),M!==(M=a(k).value)&&(R.value=(R.__value=a(k).value)??"")}),b(C,R)});var q=f(A,2);Y(()=>{a(n),ar(()=>{})}),Fe(q,5,()=>E,$e,(C,k)=>{var R=Ll(),y=c(R),M={};Y(()=>{T(y,a(k).label),M!==(M=a(k).value)&&(R.value=(R.__value=a(k).value)??"")}),b(C,R)});var I=f(_,2);{var L=C=>{var k=jl();b(C,k)},$=C=>{var k=ye(),R=ve(k);{var y=W=>{var J=ql(),D=c(J),H=c(D),ee=f(D,2);Y(()=>T(H,a(o))),K("click",ee,g),b(W,J)},M=W=>{var J=ye(),D=ve(J);{var H=G=>{var ie=$l();b(G,ie)},ee=G=>{var ie=Wl(),pe=f(c(ie),2);Fe(pe,1,()=>a(s),$e,(ue,Z)=>{var ce=Gl(),_e=c(ce),le=c(_e),Ae=c(le),Ke=f(le,2),we=c(Ke),te=f(_e,2),Ee=c(te),Re=c(Ee),Me=f(Ee,2);{var Pe=me=>{var ke=Fl(),ae=c(ke);Y(()=>T(ae,`${a(Z).passedTests??""}/${a(Z).totalTests??""} 通过`)),b(me,ke)},Le=me=>{var ke=ye(),ae=ve(ke);{var fe=he=>{var Ve=Ul(),ne=c(Ve);Y(ht=>{Ms(Ve,"title",a(Z).errorMessage),T(ne,ht)},[()=>a(Z).errorMessage.length>30?a(Z).errorMessage.substring(0,30)+"...":a(Z).errorMessage]),b(he,Ve)};Q(ae,he=>{a(Z).errorMessage&&he(fe)},!0)}b(me,ke)};Q(Me,me=>{a(Z).status==="ACCEPTED"?me(Pe):me(Le,!1)})}var be=f(te,2),Te=c(be),je=c(Te),Ne=f(be,2),xe=c(Ne);{var Ue=me=>{var ke=Bl(),ae=ve(ke),fe=f(c(ae),2),he=c(fe),Ve=f(ae,2),ne=f(c(Ve),2),ht=c(ne);Y(()=>{T(he,`${a(Z).executionTime??""}ms`),T(ht,`${a(Z).memoryUsage??""}KB`)}),b(me,ke)},ze=me=>{var ke=Vl();b(me,ke)};Q(xe,me=>{a(Z).status==="ACCEPTED"?me(Ue):me(ze,!1)})}var Be=f(Ne,2),rt=c(Be),ot=c(rt),_t=f(rt,2),Rt=c(_t);Y((me,ke,ae,fe,he)=>{T(Ae,a(Z).problem?.title||"未知题目"),T(we,`#${a(Z).problemId??""}`),Je(Ee,`background-color: ${me??""}`),T(Re,ke),T(je,ae),T(ot,fe),T(Rt,he)},[()=>m(a(Z).status),()=>O(a(Z).status),()=>P(a(Z).language),()=>B(a(Z).createdAt),()=>new Date(a(Z).createdAt).toLocaleString("zh-CN")]),K("click",ce,()=>F(a(Z))),b(ue,ce)}),b(G,ie)};Q(D,G=>{a(s).length===0?G(H):G(ee,!1)},!0)}b(W,J)};Q(R,W=>{a(o)?W(y):W(M,!1)},!0)}b(C,k)};Q(I,C=>{a(v)?C(L):C($,!1)})}yr(j,()=>a(l),C=>h(l,C)),K("input",j,V),sr(A,()=>a(i),C=>h(i,C)),K("change",A,w),sr(q,()=>a(n),C=>h(n,C)),K("change",q,w),b(e,U),tt()}var Yl=N('<main class="app svelte-1ct4udm"><!> <div class="content svelte-1ct4udm"><!></div></main>');function Kl(e,t){et(t,!1);let r=X(Ka),s=X({});pt(()=>{He.register("/",()=>{h(r,Ka),h(s,{})}),He.register("/algorithms",()=>{h(r,Gi),h(s,{})}),He.register("/algorithms/:id",o=>{h(r,tl),h(s,o)}),He.register("/problems",()=>{h(r,cl),h(s,{})}),He.register("/problems/:id",o=>{h(r,Dl),h(s,o)}),He.register("/submissions",()=>{h(r,Jl),h(s,{})}),He.setOnRouteChange(()=>{h(r,a(r))}),He.init()}),lt();var i=Yl(),n=c(i);Si(n,{});var l=f(n,2),v=c(l);ui(v,()=>a(r),(o,u)=>{u(o,{get params(){return a(s)}})}),b(e,i),tt()}ii(Kl,{target:document.getElementById("app")});
