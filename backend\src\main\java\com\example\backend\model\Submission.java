package com.example.backend.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 代码提交实体类
 * 用于存储用户提交的代码和评测结果
 */
@Entity
@Table(name = "submissions")
public class Submission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "problem_id", nullable = false)
    private Problem problem;
    
    @Column(nullable = false)
    private String language;
    
    @Column(columnDefinition = "TEXT", nullable = false)
    private String code;
    
    @Enumerated(EnumType.STRING)
    private Status status;
    
    private Integer score; // 得分（通过的测试用例数）
    
    private Integer totalTestCases; // 总测试用例数
    
    private Long executionTime; // 执行时间（毫秒）
    
    private Long memoryUsage; // 内存使用（字节）
    
    @Column(columnDefinition = "TEXT")
    private String errorMessage; // 错误信息
    
    @Column(columnDefinition = "TEXT")
    private String compileOutput; // 编译输出
    
    @Column(nullable = false)
    private LocalDateTime submitTime;
    
    private LocalDateTime judgeTime;
    
    public enum Status {
        PENDING,    // 等待评测
        JUDGING,    // 评测中
        ACCEPTED,   // 通过
        WRONG_ANSWER, // 答案错误
        TIME_LIMIT_EXCEEDED, // 超时
        MEMORY_LIMIT_EXCEEDED, // 内存超限
        RUNTIME_ERROR, // 运行时错误
        COMPILE_ERROR // 编译错误
    }
    
    // 构造函数
    public Submission() {
        this.submitTime = LocalDateTime.now();
        this.status = Status.PENDING;
    }
    
    public Submission(Problem problem, String language, String code) {
        this();
        this.problem = problem;
        this.language = language;
        this.code = code;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Problem getProblem() {
        return problem;
    }
    
    public void setProblem(Problem problem) {
        this.problem = problem;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public Integer getScore() {
        return score;
    }
    
    public void setScore(Integer score) {
        this.score = score;
    }
    
    public Integer getTotalTestCases() {
        return totalTestCases;
    }
    
    public void setTotalTestCases(Integer totalTestCases) {
        this.totalTestCases = totalTestCases;
    }
    
    public Long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    
    public Long getMemoryUsage() {
        return memoryUsage;
    }
    
    public void setMemoryUsage(Long memoryUsage) {
        this.memoryUsage = memoryUsage;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getCompileOutput() {
        return compileOutput;
    }
    
    public void setCompileOutput(String compileOutput) {
        this.compileOutput = compileOutput;
    }
    
    public LocalDateTime getSubmitTime() {
        return submitTime;
    }
    
    public void setSubmitTime(LocalDateTime submitTime) {
        this.submitTime = submitTime;
    }
    
    public LocalDateTime getJudgeTime() {
        return judgeTime;
    }
    
    public void setJudgeTime(LocalDateTime judgeTime) {
        this.judgeTime = judgeTime;
    }
}
