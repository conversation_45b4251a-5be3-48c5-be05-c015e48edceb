package com.example.backend.repository;

import com.example.backend.model.Submission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 提交记录数据访问接口
 * 提供对提交记录的CRUD操作
 */
@Repository
public interface SubmissionRepository extends JpaRepository<Submission, Long> {
    
    /**
     * 根据题目ID查找所有提交记录
     * @param problemId 题目ID
     * @return 提交记录列表
     */
    List<Submission> findByProblemIdOrderBySubmitTimeDesc(Long problemId);
    
    /**
     * 根据状态查找提交记录
     * @param status 提交状态
     * @return 提交记录列表
     */
    List<Submission> findByStatus(Submission.Status status);
    
    /**
     * 查找最近的提交记录
     * @param limit 数量限制
     * @return 提交记录列表
     */
    @Query("SELECT s FROM Submission s ORDER BY s.submitTime DESC")
    List<Submission> findRecentSubmissions(@Param("limit") int limit);
    
    /**
     * 统计题目的通过率
     * @param problemId 题目ID
     * @return 通过率统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN s.status = 'ACCEPTED' THEN 1 END) as acceptedCount, " +
           "COUNT(s) as totalCount " +
           "FROM Submission s WHERE s.problem.id = :problemId")
    Object[] getAcceptanceRate(@Param("problemId") Long problemId);
}
