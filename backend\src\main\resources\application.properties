spring.application.name=backend

# 服务器配置
server.port=8080
server.servlet.context-path=/api

# 数据库配置 (H2内存数据库)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.h2.console.enabled=true

# CORS配置
spring.web.cors.allowed-origins=http://localhost:5173
spring.web.cors.allowed-methods=*
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 日志配置
logging.level.com.example.backend=DEBUG
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
