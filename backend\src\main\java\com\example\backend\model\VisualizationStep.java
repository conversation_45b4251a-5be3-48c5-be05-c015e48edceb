package com.example.backend.model;

/**
 * 算法可视化步骤类
 * 用于表示算法执行的每一步状态
 */
public class VisualizationStep {
    
    private int stepNumber;          // 步骤编号
    private String description;      // 步骤描述
    private String dataStructure;    // 数据结构的JSON表示
    private String highlightedCode;  // 高亮的代码行
    private String variables;        // 变量状态的JSON表示
    private String operation;        // 当前操作类型
    
    // 构造函数
    public VisualizationStep() {}
    
    public VisualizationStep(int stepNumber, String description, String dataStructure) {
        this.stepNumber = stepNumber;
        this.description = description;
        this.dataStructure = dataStructure;
    }
    
    // Getter和Setter方法
    public int getStepNumber() {
        return stepNumber;
    }
    
    public void setStepNumber(int stepNumber) {
        this.stepNumber = stepNumber;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getDataStructure() {
        return dataStructure;
    }
    
    public void setDataStructure(String dataStructure) {
        this.dataStructure = dataStructure;
    }
    
    public String getHighlightedCode() {
        return highlightedCode;
    }
    
    public void setHighlightedCode(String highlightedCode) {
        this.highlightedCode = highlightedCode;
    }
    
    public String getVariables() {
        return variables;
    }
    
    public void setVariables(String variables) {
        this.variables = variables;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public void setOperation(String operation) {
        this.operation = operation;
    }
}
